import Database from 'better-sqlite3';
import path from 'path';
import fs from 'fs';
import { createModuleLogger } from './logger';

const logger = createModuleLogger('database');

export interface DatabaseConfig {
  path: string;
  readonly?: boolean;
  verbose?: boolean;
}

export class DatabaseService {
  private db: Database.Database;
  private static instance: DatabaseService;

  private constructor(config: DatabaseConfig) {
    // Ensure directory exists
    const dbDir = path.dirname(config.path);
    if (!fs.existsSync(dbDir)) {
      fs.mkdirSync(dbDir, { recursive: true });
      logger.info(`Created database directory: ${dbDir}`);
    }

    // Initialize database
    this.db = new Database(config.path, {
      readonly: config.readonly || false,
      verbose: config.verbose ? logger.debug.bind(logger) : undefined
    });

    // Enable WAL mode for better concurrency
    this.db.pragma('journal_mode = WAL');

    // Enable foreign keys
    this.db.pragma('foreign_keys = ON');

    logger.info(`Database initialized at: ${config.path}`);
  }

  public static getInstance(config?: DatabaseConfig): DatabaseService {
    if (!DatabaseService.instance) {
      if (!config) {
        throw new Error('Database config required for first initialization');
      }
      DatabaseService.instance = new DatabaseService(config);
    }
    return DatabaseService.instance;
  }

  public getDatabase(): Database.Database {
    return this.db;
  }

  public async healthCheck(): Promise<boolean> {
    try {
      const result = this.db.prepare('SELECT 1 as health').get() as { health: number };
      return result.health === 1;
    } catch (error) {
      logger.error('Database health check failed:', error);
      return false;
    }
  }

  public close(): void {
    if (this.db) {
      this.db.close();
      logger.info('Database connection closed');
    }
  }

  // Transaction helper
  public transaction<T>(fn: () => T): T {
    const transaction = this.db.transaction(fn);
    return transaction();
  }

  // Backup helper
  public backup(backupPath: string): void {
    try {
      this.db.backup(backupPath);
      logger.info(`Database backed up to: ${backupPath}`);
    } catch (error) {
      logger.error('Database backup failed:', error);
      throw error;
    }
  }
}

// Initialize database service
const dbPath = process.env.DATABASE_PATH || './data/database.sqlite';
export const databaseService = DatabaseService.getInstance({
  path: dbPath,
  verbose: process.env.NODE_ENV === 'development'
});

export default databaseService;