{"level":"info","message":"Created database directory: ./data","module":"database","timestamp":"2025-07-01 18:11:53"}
{"level":"info","message":"Database initialized at: ./data/database.sqlite","module":"database","timestamp":"2025-07-01 18:11:53"}
{"level":"info","message":"Server running on port 3001","module":"server","timestamp":"2025-07-01 18:11:53"}
{"level":"info","message":"Environment: development","module":"server","timestamp":"2025-07-01 18:11:53"}
{"ip":"::ffff:127.0.0.1","level":"info","message":"GET /api/health","module":"server","timestamp":"2025-07-01 18:12:00","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:127.0.0.1","level":"info","message":"GET /api/sync/status","module":"server","timestamp":"2025-07-01 18:12:00","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:127.0.0.1","level":"info","message":"GET /api/sync/history","module":"server","timestamp":"2025-07-01 18:12:00","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::ffff:127.0.0.1","level":"info","message":"GET /api/health","module":"server","timestamp":"2025-07-01 18:12:08","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:127.0.0.1","level":"info","message":"GET /api/sync/status","module":"server","timestamp":"2025-07-01 18:12:08","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:127.0.0.1","level":"info","message":"GET /api/sync/history","module":"server","timestamp":"2025-07-01 18:12:08","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:127.0.0.1","level":"info","message":"GET /api/sync/status","module":"server","timestamp":"2025-07-01 18:12:11","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:127.0.0.1","level":"info","message":"GET /api/sync/history","module":"server","timestamp":"2025-07-01 18:12:11","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:127.0.0.1","level":"info","message":"GET /api/health","module":"server","timestamp":"2025-07-01 18:12:12","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:127.0.0.1","level":"info","message":"GET /api/sync/status","module":"server","timestamp":"2025-07-01 18:12:12","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:127.0.0.1","level":"info","message":"GET /api/sync/history","module":"server","timestamp":"2025-07-01 18:12:12","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:127.0.0.1","level":"info","message":"GET /api/woocommerce/test","module":"server","timestamp":"2025-07-01 18:12:14","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:127.0.0.1","level":"info","message":"GET /api/health","module":"server","timestamp":"2025-07-01 18:12:28","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:127.0.0.1","level":"info","message":"GET /api/sync/status","module":"server","timestamp":"2025-07-01 18:12:28","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:127.0.0.1","level":"info","message":"GET /api/sync/history","module":"server","timestamp":"2025-07-01 18:12:28","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:127.0.0.1","level":"info","message":"GET /api/health","module":"server","timestamp":"2025-07-01 18:12:41","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:127.0.0.1","level":"info","message":"GET /api/sync/status","module":"server","timestamp":"2025-07-01 18:12:41","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:127.0.0.1","level":"info","message":"GET /api/sync/history","module":"server","timestamp":"2025-07-01 18:12:41","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:127.0.0.1","level":"info","message":"GET /api/health","module":"server","timestamp":"2025-07-01 18:12:51","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:127.0.0.1","level":"info","message":"GET /api/sync/status","module":"server","timestamp":"2025-07-01 18:12:51","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:127.0.0.1","level":"info","message":"GET /api/sync/history","module":"server","timestamp":"2025-07-01 18:12:51","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"Received SIGINT, shutting down gracefully","module":"server","timestamp":"2025-07-01 18:17:40"}
{"level":"info","message":"Database initialized at: ./data/database.sqlite","module":"database","timestamp":"2025-07-01 21:23:52"}
{"level":"info","message":"Server running on port 3001","module":"server","timestamp":"2025-07-01 21:23:52"}
{"level":"info","message":"Environment: development","module":"server","timestamp":"2025-07-01 21:23:52"}
{"level":"info","message":"Received SIGINT, shutting down gracefully","module":"server","timestamp":"2025-07-01 21:23:52"}
{"level":"info","message":"Database initialized at: ./data/database.sqlite","module":"database","timestamp":"2025-07-01 21:23:57"}
{"level":"info","message":"Server running on port 3001","module":"server","timestamp":"2025-07-01 21:23:57"}
{"level":"info","message":"Environment: development","module":"server","timestamp":"2025-07-01 21:23:57"}
{"ip":"::ffff:127.0.0.1","level":"info","message":"GET /api/health","module":"server","timestamp":"2025-07-01 21:24:03","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:127.0.0.1","level":"info","message":"GET /api/sync/status","module":"server","timestamp":"2025-07-01 21:24:03","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:127.0.0.1","level":"info","message":"GET /api/sync/history","module":"server","timestamp":"2025-07-01 21:24:03","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:127.0.0.1","level":"info","message":"GET /api/woocommerce/test","module":"server","timestamp":"2025-07-01 21:24:05","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"Received SIGTERM, shutting down gracefully","module":"server","timestamp":"2025-07-01 21:26:44"}
{"level":"info","message":"Database initialized at: ./data/database.sqlite","module":"database","timestamp":"2025-07-01 21:26:45"}
{"level":"info","message":"Server running on port 3001","module":"server","timestamp":"2025-07-01 21:26:45"}
{"level":"info","message":"Environment: development","module":"server","timestamp":"2025-07-01 21:26:45"}
{"ip":"::ffff:127.0.0.1","level":"info","message":"GET /api/health","module":"server","timestamp":"2025-07-01 21:26:51","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:127.0.0.1","level":"info","message":"GET /api/sync/status","module":"server","timestamp":"2025-07-01 21:26:51","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:127.0.0.1","level":"info","message":"GET /api/sync/history","module":"server","timestamp":"2025-07-01 21:26:51","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:127.0.0.1","level":"info","message":"GET /api/woocommerce/test","module":"server","timestamp":"2025-07-01 21:26:52","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:127.0.0.1","level":"info","message":"GET /api/woocommerce/test","module":"server","timestamp":"2025-07-01 21:27:01","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"Received SIGTERM, shutting down gracefully","module":"server","timestamp":"2025-07-01 21:27:50"}
{"level":"info","message":"Database initialized at: ./data/database.sqlite","module":"database","timestamp":"2025-07-01 21:27:50"}
{"level":"info","message":"Server running on port 3001","module":"server","timestamp":"2025-07-01 21:27:50"}
{"level":"info","message":"Environment: development","module":"server","timestamp":"2025-07-01 21:27:50"}
{"ip":"::ffff:127.0.0.1","level":"info","message":"GET /api/woocommerce/test","module":"server","timestamp":"2025-07-01 21:27:53","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:127.0.0.1","level":"info","message":"GET /api/woocommerce/test","module":"server","timestamp":"2025-07-01 21:27:54","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:127.0.0.1","level":"info","message":"GET /api/health","module":"server","timestamp":"2025-07-01 21:27:56","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:127.0.0.1","level":"info","message":"GET /api/sync/status","module":"server","timestamp":"2025-07-01 21:27:56","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:127.0.0.1","level":"info","message":"GET /api/sync/history","module":"server","timestamp":"2025-07-01 21:27:56","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:127.0.0.1","level":"info","message":"GET /api/woocommerce/test","module":"server","timestamp":"2025-07-01 21:27:58","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"Received SIGINT, shutting down gracefully","module":"server","timestamp":"2025-07-01 21:28:00"}
{"level":"info","message":"Database initialized at: ./data/database.sqlite","module":"database","timestamp":"2025-07-01 21:28:03"}
{"level":"info","message":"Server running on port 3001","module":"server","timestamp":"2025-07-01 21:28:03"}
{"level":"info","message":"Environment: development","module":"server","timestamp":"2025-07-01 21:28:03"}
{"ip":"::ffff:127.0.0.1","level":"info","message":"GET /api/health","module":"server","timestamp":"2025-07-01 21:28:05","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:127.0.0.1","level":"info","message":"GET /api/sync/status","module":"server","timestamp":"2025-07-01 21:28:05","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:127.0.0.1","level":"info","message":"GET /api/sync/history","module":"server","timestamp":"2025-07-01 21:28:05","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:127.0.0.1","level":"info","message":"GET /api/woocommerce/test","module":"server","timestamp":"2025-07-01 21:28:10","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"Received SIGTERM, shutting down gracefully","module":"server","timestamp":"2025-07-01 21:28:30"}
{"level":"info","message":"Database initialized at: ./data/database.sqlite","module":"database","timestamp":"2025-07-01 21:28:31"}
{"level":"info","message":"Server running on port 3001","module":"server","timestamp":"2025-07-01 21:28:31"}
{"level":"info","message":"Environment: development","module":"server","timestamp":"2025-07-01 21:28:31"}
{"ip":"::ffff:127.0.0.1","level":"info","message":"GET /api/woocommerce/test","module":"server","timestamp":"2025-07-01 21:28:34","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"Received SIGTERM, shutting down gracefully","module":"server","timestamp":"2025-07-01 21:30:46"}
{"level":"info","message":"Database initialized at: ./data/database.sqlite","module":"database","timestamp":"2025-07-01 21:30:47"}
{"level":"info","message":"Server running on port 3001","module":"server","timestamp":"2025-07-01 21:30:47"}
{"level":"info","message":"Environment: development","module":"server","timestamp":"2025-07-01 21:30:47"}
{"ip":"::ffff:127.0.0.1","level":"info","message":"GET /api/health","module":"server","timestamp":"2025-07-01 21:30:50","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:127.0.0.1","level":"info","message":"GET /api/sync/status","module":"server","timestamp":"2025-07-01 21:30:50","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:127.0.0.1","level":"info","message":"GET /api/sync/history","module":"server","timestamp":"2025-07-01 21:30:50","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:127.0.0.1","level":"info","message":"GET /api/woocommerce/test","module":"server","timestamp":"2025-07-01 21:30:51","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"Received SIGTERM, shutting down gracefully","module":"server","timestamp":"2025-07-01 21:32:42"}
{"level":"info","message":"Database initialized at: ./data/database.sqlite","module":"database","timestamp":"2025-07-01 21:32:43"}
{"level":"info","message":"Server running on port 3001","module":"server","timestamp":"2025-07-01 21:32:43"}
{"level":"info","message":"Environment: development","module":"server","timestamp":"2025-07-01 21:32:43"}
{"ip":"::ffff:127.0.0.1","level":"info","message":"GET /api/woocommerce/test","module":"server","timestamp":"2025-07-01 21:32:46","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"Received SIGINT, shutting down gracefully","module":"server","timestamp":"2025-07-01 21:33:09"}
{"level":"info","message":"Database initialized at: ./data/database.sqlite","module":"database","timestamp":"2025-07-01 21:33:35"}
{"level":"info","message":"Server running on port 3001","module":"server","timestamp":"2025-07-01 21:33:35"}
{"level":"info","message":"Environment: development","module":"server","timestamp":"2025-07-01 21:33:35"}
{"ip":"::ffff:127.0.0.1","level":"info","message":"GET /api/health","module":"server","timestamp":"2025-07-01 21:33:47","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:127.0.0.1","level":"info","message":"GET /api/sync/status","module":"server","timestamp":"2025-07-01 21:33:47","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:127.0.0.1","level":"info","message":"GET /api/sync/history","module":"server","timestamp":"2025-07-01 21:33:47","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"Received SIGINT, shutting down gracefully","module":"server","timestamp":"2025-07-01 21:33:56"}
{"level":"info","message":"Database initialized at: ./data/database.sqlite","module":"database","timestamp":"2025-07-01 21:35:04"}
{"level":"info","message":"Server running on port 3001","module":"server","timestamp":"2025-07-01 21:35:04"}
{"level":"info","message":"Environment: development","module":"server","timestamp":"2025-07-01 21:35:04"}
{"level":"info","message":"Received SIGTERM, shutting down gracefully","module":"server","timestamp":"2025-07-01 21:36:07"}
{"level":"info","message":"Database initialized at: ./data/database.sqlite","module":"database","timestamp":"2025-07-01 21:36:07"}
{"level":"info","message":"Server running on port 3001","module":"server","timestamp":"2025-07-01 21:36:07"}
{"level":"info","message":"Environment: development","module":"server","timestamp":"2025-07-01 21:36:07"}
{"level":"info","message":"Received SIGTERM, shutting down gracefully","module":"server","timestamp":"2025-07-01 21:36:55"}
{"level":"info","message":"Database initialized at: ./data/database.sqlite","module":"database","timestamp":"2025-07-01 21:36:56"}
{"level":"info","message":"Server running on port 3001","module":"server","timestamp":"2025-07-01 21:36:56"}
{"level":"info","message":"Environment: development","module":"server","timestamp":"2025-07-01 21:36:56"}
{"level":"info","message":"Received SIGTERM, shutting down gracefully","module":"server","timestamp":"2025-07-01 21:37:10"}
{"level":"info","message":"Database initialized at: ./data/database.sqlite","module":"database","timestamp":"2025-07-01 21:37:11"}
{"level":"info","message":"Server running on port 3001","module":"server","timestamp":"2025-07-01 21:37:11"}
{"level":"info","message":"Environment: development","module":"server","timestamp":"2025-07-01 21:37:11"}
{"level":"info","message":"Received SIGTERM, shutting down gracefully","module":"server","timestamp":"2025-07-01 21:37:27"}
{"level":"info","message":"Database initialized at: ./data/database.sqlite","module":"database","timestamp":"2025-07-01 21:37:27"}
{"level":"info","message":"Server running on port 3001","module":"server","timestamp":"2025-07-01 21:37:27"}
{"level":"info","message":"Environment: development","module":"server","timestamp":"2025-07-01 21:37:27"}
{"ip":"::ffff:127.0.0.1","level":"info","message":"GET /api/health","module":"server","timestamp":"2025-07-01 21:37:32","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:127.0.0.1","level":"info","message":"GET /api/sync/status","module":"server","timestamp":"2025-07-01 21:37:32","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:127.0.0.1","level":"info","message":"GET /api/sync/history","module":"server","timestamp":"2025-07-01 21:37:32","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:127.0.0.1","level":"info","message":"GET /api/woocommerce/test","module":"server","timestamp":"2025-07-01 21:37:34","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"Received SIGTERM, shutting down gracefully","module":"server","timestamp":"2025-07-01 21:37:58"}
{"level":"info","message":"Database initialized at: ./data/database.sqlite","module":"database","timestamp":"2025-07-01 21:37:58"}
{"level":"info","message":"Server running on port 3001","module":"server","timestamp":"2025-07-01 21:37:58"}
{"level":"info","message":"Environment: development","module":"server","timestamp":"2025-07-01 21:37:58"}
{"ip":"::ffff:127.0.0.1","level":"info","message":"GET /api/woocommerce/test","module":"server","timestamp":"2025-07-01 21:38:01","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"Received SIGINT, shutting down gracefully","module":"server","timestamp":"2025-07-01 21:38:09"}
{"level":"info","message":"Database initialized at: ./data/database.sqlite","module":"database","timestamp":"2025-07-01 21:38:12"}
{"level":"info","message":"Server running on port 3001","module":"server","timestamp":"2025-07-01 21:38:12"}
{"level":"info","message":"Environment: development","module":"server","timestamp":"2025-07-01 21:38:12"}
{"ip":"::ffff:127.0.0.1","level":"info","message":"GET /api/health","module":"server","timestamp":"2025-07-01 21:38:14","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:127.0.0.1","level":"info","message":"GET /api/sync/status","module":"server","timestamp":"2025-07-01 21:38:14","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:127.0.0.1","level":"info","message":"GET /api/sync/history","module":"server","timestamp":"2025-07-01 21:38:14","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:127.0.0.1","level":"info","message":"GET /api/woocommerce/test","module":"server","timestamp":"2025-07-01 21:38:17","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"Received SIGTERM, shutting down gracefully","module":"server","timestamp":"2025-07-01 21:39:12"}
{"level":"info","message":"Database initialized at: ./data/database.sqlite","module":"database","timestamp":"2025-07-01 21:39:12"}
{"level":"info","message":"Server running on port 3001","module":"server","timestamp":"2025-07-01 21:39:12"}
{"level":"info","message":"Environment: development","module":"server","timestamp":"2025-07-01 21:39:12"}
{"ip":"::ffff:127.0.0.1","level":"info","message":"GET /api/woocommerce/test","module":"server","timestamp":"2025-07-01 21:39:18","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"Received SIGTERM, shutting down gracefully","module":"server","timestamp":"2025-07-01 21:39:46"}
{"level":"info","message":"Database initialized at: ./data/database.sqlite","module":"database","timestamp":"2025-07-01 21:39:46"}
{"level":"info","message":"Server running on port 3001","module":"server","timestamp":"2025-07-01 21:39:46"}
{"level":"info","message":"Environment: development","module":"server","timestamp":"2025-07-01 21:39:46"}
{"level":"info","message":"Received SIGTERM, shutting down gracefully","module":"server","timestamp":"2025-07-01 21:40:05"}
{"level":"info","message":"Database initialized at: ./data/database.sqlite","module":"database","timestamp":"2025-07-01 21:40:06"}
{"level":"info","message":"Server running on port 3001","module":"server","timestamp":"2025-07-01 21:40:06"}
{"level":"info","message":"Environment: development","module":"server","timestamp":"2025-07-01 21:40:06"}
{"level":"info","message":"Received SIGTERM, shutting down gracefully","module":"server","timestamp":"2025-07-01 21:40:20"}
{"level":"info","message":"Database initialized at: ./data/database.sqlite","module":"database","timestamp":"2025-07-01 21:40:21"}
{"level":"info","message":"Server running on port 3001","module":"server","timestamp":"2025-07-01 21:40:21"}
{"level":"info","message":"Environment: development","module":"server","timestamp":"2025-07-01 21:40:21"}
{"level":"info","message":"Received SIGTERM, shutting down gracefully","module":"server","timestamp":"2025-07-01 21:45:20"}
{"level":"info","message":"Database initialized at: ./data/database.sqlite","module":"database","timestamp":"2025-07-01 21:45:46"}
{"level":"info","message":"WooCommerce API client initialized","module":"woocommerce","timestamp":"2025-07-01 21:45:46","url":"https://dev-enengl.k-tools.net","version":"wc/v3"}
{"level":"info","message":"Server running on port 3001","module":"server","timestamp":"2025-07-01 21:45:46"}
{"level":"info","message":"Environment: development","module":"server","timestamp":"2025-07-01 21:45:46"}
{"ip":"::ffff:127.0.0.1","level":"info","message":"GET /api/health","module":"server","timestamp":"2025-07-01 21:45:50","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:127.0.0.1","level":"info","message":"GET /api/sync/status","module":"server","timestamp":"2025-07-01 21:45:50","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:127.0.0.1","level":"info","message":"GET /api/sync/history","module":"server","timestamp":"2025-07-01 21:45:50","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:127.0.0.1","level":"info","message":"GET /api/woocommerce/test","module":"server","timestamp":"2025-07-01 21:45:51","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"Received SIGTERM, shutting down gracefully","module":"server","timestamp":"2025-07-01 21:47:00"}
{"level":"info","message":"Database initialized at: ./data/database.sqlite","module":"database","timestamp":"2025-07-01 21:47:00"}
{"level":"info","message":"Server running on port 3001","module":"server","timestamp":"2025-07-01 21:47:00"}
{"level":"info","message":"Environment: development","module":"server","timestamp":"2025-07-01 21:47:00"}
{"level":"info","message":"Received SIGTERM, shutting down gracefully","module":"server","timestamp":"2025-07-01 21:47:33"}
{"level":"info","message":"Database initialized at: ./data/database.sqlite","module":"database","timestamp":"2025-07-01 21:47:33"}
{"level":"info","message":"Server running on port 3001","module":"server","timestamp":"2025-07-01 21:47:33"}
{"level":"info","message":"Environment: development","module":"server","timestamp":"2025-07-01 21:47:33"}
{"level":"info","message":"Received SIGTERM, shutting down gracefully","module":"server","timestamp":"2025-07-01 21:47:48"}
{"level":"info","message":"Database initialized at: ./data/database.sqlite","module":"database","timestamp":"2025-07-01 21:48:04"}
{"level":"info","message":"WooCommerce API client initialized","module":"woocommerce","timestamp":"2025-07-01 21:48:04","url":"https://dev-enengl.k-tools.net","version":"wc/v3"}
{"level":"info","message":"Server running on port 3001","module":"server","timestamp":"2025-07-01 21:48:04"}
{"level":"info","message":"Environment: development","module":"server","timestamp":"2025-07-01 21:48:04"}
{"ip":"::ffff:127.0.0.1","level":"info","message":"GET /api/woocommerce/test","module":"server","timestamp":"2025-07-01 21:48:07","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"level":"info","message":"Received SIGINT, shutting down gracefully","module":"server","timestamp":"2025-07-01 21:48:13"}
{"level":"info","message":"Database initialized at: ./data/database.sqlite","module":"database","timestamp":"2025-07-01 21:48:15"}
{"level":"info","message":"WooCommerce API client initialized","module":"woocommerce","timestamp":"2025-07-01 21:48:15","url":"https://dev-enengl.k-tools.net","version":"wc/v3"}
{"level":"info","message":"Server running on port 3001","module":"server","timestamp":"2025-07-01 21:48:15"}
{"level":"info","message":"Environment: development","module":"server","timestamp":"2025-07-01 21:48:15"}
{"ip":"::ffff:127.0.0.1","level":"info","message":"GET /api/health","module":"server","timestamp":"2025-07-01 21:48:21","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:127.0.0.1","level":"info","message":"GET /api/sync/status","module":"server","timestamp":"2025-07-01 21:48:21","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:127.0.0.1","level":"info","message":"GET /api/sync/history","module":"server","timestamp":"2025-07-01 21:48:21","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0"}
{"ip":"::ffff:127.0.0.1","level":"info","message":"GET /api/woocommerce/test","module":"server","timestamp":"2025-07-01 21:48:23","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0"}
