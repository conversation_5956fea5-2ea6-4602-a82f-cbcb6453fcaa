/**
 * TypeScript types for RZA export data structures
 * This file contains only the type definitions without any dependencies
 */

// RZA Export Types
export interface RzaField {
  fieldnumber: string;
  value: string;
}

export interface RzaTaxRate {
  countryID: number;
  tax: number;
}

export interface RzaTranslation {
  languageId: number;
  name: string;
  longdescription: string;
}

export interface RzaPrice {
  price: number;
  percent?: string;
  pricegroup: string;
  from: number;
}

export interface RzaTextilartikel {
  kennzeichen?: string;
  textilartikelID?: string;
  groessentabelleID?: string;
  groessenID?: string;
  farbtabelleID?: string;
  farbenID?: string;
  saisonID?: string;
  preiszuschlag?: string;
}

export interface RzaArticle {
  ordernumber: string;
  rzaArtikelID: number;
  artGroupID: number;
  artSubGroupID: number;
  categories: number[];
  name: string;
  ean: string;
  barcode: string;
  unitID: string;
  instock: number;
  stock: number;
  weight: number;
  active: number;
  description_long: string;
  tax: number;
  taxRates: RzaTaxRate[];
  suppliernumbers: string[];
  shippingtime: string;
  fields: RzaField[];
  translations: RzaTranslation[];
  prices: RzaPrice[];
  textilartikel: RzaTextilartikel;
  onlineshopstatus: number;
}

export interface RzaDiscount {
  fromDate: string;
  toDate: string;
  percent: number;
  pricegroup: string;
}

export interface RzaArtGroup {
  ID: number;
  number: number;
  name: string;
  discounts: RzaDiscount[];
}

export interface RzaArtSubGroup {
  ID: number;
  number: number;
  name: string;
  discounts?: RzaDiscount[];
}

export interface RzaCategory {
  ID: number;
  ParentID?: number;
  name: string;
}

export interface RzaCountry {
  ID: number;
  name: string;
  ISO: string;
}

export interface RzaTotalDiscount {
  discount: string;
  from: string;
  to: string;
}

export interface RzaPriceAgreement {
  rzaArtikelID: string;
  artGroupID: string;
  artSubGroupID: string;
  discount: string;
  price: string;
  from: string;
  fromDate: string;
  toDate: string;
}

export interface RzaCustomer {
  rzaAddressId: string;
  customernumber: string;
  pricegroup: string;
  fields: RzaField[];
  totaldiscount: RzaTotalDiscount;
  Anrede: string;
  Titel: string;
  Zuname: string;
  Vorname: string;
  Namenszeile2: string;
  Namenszeile3: string;
  Land: string;
  PLZ: string;
  Ort: string;
  Strasse: string;
  UID: string;
  Mail: string;
  Telefon1: string;
  Telefon2: string;
  Fax: string;
  Kreditlimit: number;
  Liefersperre: number;
  countryID: number;
  priceagreements: RzaPriceAgreement[];
}

export interface RzaOrder {
  ordernumber: string;
  orderstatus: string;
}

export interface RzaGroupDefinition {
  artGroups: RzaArtGroup[];
  artSubGroups: RzaArtSubGroup[];
}

export interface RzaExport {
  articles: RzaArticle[];
  customers: RzaCustomer[];
  orders: RzaOrder[];
  groupDefinition: RzaGroupDefinition;
  categories: RzaCategory[];
  countries: RzaCountry[];
}

// WooCommerce Product interface (standalone version)
export interface WooCommerceProduct {
  id?: number;
  name: string;
  slug?: string;
  type?: string;
  status?: string;
  featured?: boolean;
  catalog_visibility?: string;
  description?: string;
  short_description?: string;
  sku?: string;
  price?: string;
  regular_price?: string;
  sale_price?: string;
  manage_stock?: boolean;
  stock_quantity?: number;
  stock_status?: string;
  categories?: Array<{ id: number; name?: string }>;
  images?: Array<{ src: string; alt?: string }>;
  weight?: string;
  dimensions?: {
    length?: string;
    width?: string;
    height?: string;
  };
  tax_status?: string;
  tax_class?: string;
  meta_data?: Array<{ key: string; value: string }>;
}
