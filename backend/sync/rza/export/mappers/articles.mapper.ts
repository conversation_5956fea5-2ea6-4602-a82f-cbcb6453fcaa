/**
 * Article mapping functions for RZA to WooCommerce conversion
 * Handles product data transformation with best practices
 */

import {
  RzaArticle,
  WooCommerceProduct,
  ArticleMappingConfig,
  RzaTextilartikel
} from '../models/article.model';
import { RzaPrice, RzaTaxRate } from '../models/common.model';
import { RzaCategory } from '../models/category.model';

/**
 * Default configuration for article mapping
 */
export const DEFAULT_ARTICLE_CONFIG: ArticleMappingConfig = {
  defaultPriceGroup: 'VK-Preis',
  defaultLanguageId: 1,
  includeInactiveProducts: false,
  stockThreshold: 0,
  defaultTaxClass: ''
};

/**
 * Maps a single RZA article to WooCommerce product
 * 
 * @param rzaArticle - RZA article data
 * @param categories - Available categories for mapping
 * @param config - Mapping configuration
 * @returns WooCommerce product object
 */
export function mapRzaArticleToWooProduct(
  rzaArticle: RzaArticle,
  categories: RzaCategory[] = [],
  config: Partial<ArticleMappingConfig> = {}
): WooCommerceProduct {
  const mappingConfig = { ...DEFAULT_ARTICLE_CONFIG, ...config };

  // Get primary price for the configured price group
  const primaryPrice = findBestPrice(rzaArticle.prices, mappingConfig.defaultPriceGroup, 1);
  
  // Check for quantity-based sale price
  const salePrice = findBestPrice(rzaArticle.prices, mappingConfig.defaultPriceGroup, 10);

  // Map categories to WooCommerce format
  const wooCategories = mapCategories(rzaArticle.categories, categories);

  // Determine product status and stock
  const status = determineProductStatus(rzaArticle, mappingConfig);
  const stockInfo = calculateStockInfo(rzaArticle, mappingConfig);

  // Get tax configuration
  const taxConfig = mapTaxConfiguration(rzaArticle, mappingConfig);

  // Build the WooCommerce product
  const wooProduct: WooCommerceProduct = {
    name: rzaArticle.name,
    slug: generateProductSlug(rzaArticle.ordernumber, rzaArticle.name),
    type: determineProductType(rzaArticle),
    status: status,
    featured: false,
    catalog_visibility: 'visible',
    description: cleanHtmlDescription(rzaArticle.description_long),
    short_description: generateShortDescription(rzaArticle),
    sku: rzaArticle.ordernumber,
    price: primaryPrice?.price.toString() || '0',
    regular_price: primaryPrice?.price.toString() || '0',
    manage_stock: true,
    stock_quantity: stockInfo.quantity,
    stock_status: stockInfo.status,
    categories: wooCategories,
    weight: rzaArticle.weight.toString(),
    tax_status: taxConfig.tax_status,
    tax_class: taxConfig.tax_class,
    meta_data: buildMetaData(rzaArticle)
  };

  // Add sale price if applicable
  if (salePrice && salePrice.price < (primaryPrice?.price || 0)) {
    wooProduct.sale_price = salePrice.price.toString();
  }

  return wooProduct;
}

/**
 * Maps multiple RZA articles to WooCommerce products
 * 
 * @param rzaArticles - Array of RZA articles
 * @param categories - Available categories
 * @param config - Mapping configuration
 * @returns Array of WooCommerce products
 */
export function mapRzaArticlesToWooProducts(
  rzaArticles: RzaArticle[],
  categories: RzaCategory[] = [],
  config: Partial<ArticleMappingConfig> = {}
): WooCommerceProduct[] {
  const mappingConfig = { ...DEFAULT_ARTICLE_CONFIG, ...config };

  return rzaArticles
    .filter(article => shouldIncludeArticle(article, mappingConfig))
    .map(article => mapRzaArticleToWooProduct(article, categories, mappingConfig));
}

/**
 * Finds the best price for given price group and quantity
 * 
 * @param prices - Array of RZA prices
 * @param priceGroup - Price group to search for
 * @param quantity - Quantity for price break calculation
 * @returns Best matching price or undefined
 */
export function findBestPrice(
  prices: RzaPrice[],
  priceGroup: string,
  quantity: number = 1
): RzaPrice | undefined {
  const groupPrices = prices
    .filter(price => price.pricegroup === priceGroup)
    .sort((a, b) => b.from - a.from); // Sort by quantity descending

  return groupPrices.find(price => quantity >= price.from);
}

/**
 * Maps RZA categories to WooCommerce category format
 * 
 * @param categoryIds - Array of RZA category IDs
 * @param availableCategories - Available categories for lookup
 * @returns Array of WooCommerce category objects
 */
function mapCategories(
  categoryIds: number[],
  availableCategories: RzaCategory[]
): Array<{ id: number; name?: string }> {
  return categoryIds
    .map(categoryId => {
      const category = availableCategories.find(cat => cat.ID === categoryId);
      return category 
        ? { id: category.ID, name: category.name }
        : { id: categoryId };
    })
    .filter(Boolean);
}

/**
 * Determines if an article should be included in the sync
 * 
 * @param article - RZA article to check
 * @param config - Mapping configuration
 * @returns True if article should be included
 */
function shouldIncludeArticle(
  article: RzaArticle,
  config: ArticleMappingConfig
): boolean {
  // Skip inactive products if not configured to include them
  if (!config.includeInactiveProducts && article.active !== 1) {
    return false;
  }

  // Skip products not marked for online shop
  if (article.onlineshopstatus !== 1) {
    return false;
  }

  return true;
}

/**
 * Determines WooCommerce product status based on RZA flags
 * 
 * @param article - RZA article
 * @param config - Mapping configuration
 * @returns WooCommerce product status
 */
function determineProductStatus(
  article: RzaArticle,
  config: ArticleMappingConfig
): 'publish' | 'draft' {
  if (article.active === 1 && article.onlineshopstatus === 1) {
    return 'publish';
  }
  return 'draft';
}

/**
 * Calculates stock information for WooCommerce
 * 
 * @param article - RZA article
 * @param config - Mapping configuration
 * @returns Stock quantity and status
 */
function calculateStockInfo(
  article: RzaArticle,
  config: ArticleMappingConfig
): { quantity: number; status: 'instock' | 'outofstock' | 'onbackorder' } {
  const quantity = Math.max(0, article.instock);
  const status = quantity > config.stockThreshold ? 'instock' : 'outofstock';
  
  return { quantity, status };
}

/**
 * Maps RZA tax configuration to WooCommerce
 * 
 * @param article - RZA article with tax information
 * @param config - Mapping configuration
 * @returns Tax configuration object
 */
function mapTaxConfiguration(
  article: RzaArticle,
  config: ArticleMappingConfig
): { tax_status: 'taxable' | 'shipping' | 'none'; tax_class: string } {
  return {
    tax_status: article.tax > 0 ? 'taxable' : 'none',
    tax_class: config.defaultTaxClass
  };
}

/**
 * Determines WooCommerce product type
 * 
 * @param article - RZA article
 * @returns Product type
 */
function determineProductType(article: RzaArticle): 'simple' | 'variable' {
  // Check if this is a textile article with variations
  if (article.textilartikel?.kennzeichen === '1') {
    return 'variable'; // Main textile article with variations
  }
  
  return 'simple';
}

/**
 * Generates a WooCommerce-compatible product slug
 * 
 * @param ordernumber - RZA order number
 * @param name - Product name
 * @returns URL-safe slug
 */
function generateProductSlug(ordernumber: string, name: string): string {
  const baseSlug = `${ordernumber}-${name}`
    .toLowerCase()
    .replace(/[^a-z0-9\s-]/g, '') // Remove special characters
    .replace(/\s+/g, '-') // Replace spaces with hyphens
    .replace(/-+/g, '-') // Replace multiple hyphens with single
    .replace(/^-|-$/g, ''); // Remove leading/trailing hyphens

  return baseSlug.substring(0, 100); // Limit length
}

/**
 * Cleans HTML description for WooCommerce
 * 
 * @param htmlDescription - Raw HTML description
 * @returns Cleaned HTML description
 */
function cleanHtmlDescription(htmlDescription: string): string {
  if (!htmlDescription) return '';
  
  // Basic HTML cleaning - remove CDATA wrapper if present
  return htmlDescription
    .replace(/^<!\[CDATA\[/, '')
    .replace(/\]\]>$/, '')
    .trim();
}

/**
 * Generates a short description from article data
 * 
 * @param article - RZA article
 * @returns Short description
 */
function generateShortDescription(article: RzaArticle): string {
  // Use first custom field with content, or generate from name
  const customField = article.fields.find(field => field.value && field.value.trim());
  if (customField) {
    return customField.value.substring(0, 160);
  }

  // Generate from product name and key attributes
  const parts = [article.name];
  if (article.unitID && article.unitID !== 'stk') {
    parts.push(`Unit: ${article.unitID}`);
  }
  if (article.weight > 0) {
    parts.push(`Weight: ${article.weight}kg`);
  }

  return parts.join(' | ').substring(0, 160);
}

/**
 * Builds meta data array for WooCommerce product
 * 
 * @param article - RZA article
 * @returns Array of meta data objects
 */
function buildMetaData(article: RzaArticle): Array<{ key: string; value: string }> {
  const metaData: Array<{ key: string; value: string }> = [
    { key: '_rza_artikel_id', value: article.rzaArtikelID.toString() },
    { key: '_rza_art_group_id', value: article.artGroupID.toString() },
    { key: '_rza_art_sub_group_id', value: article.artSubGroupID.toString() },
    { key: '_rza_unit_id', value: article.unitID },
    { key: '_rza_ean', value: article.ean },
    { key: '_rza_barcode', value: article.barcode },
    { key: '_rza_tax_rate', value: article.tax.toString() },
    { key: '_rza_shipping_time', value: article.shippingtime },
    { key: '_rza_stock', value: article.stock.toString() }
  ];

  // Add supplier numbers
  if (article.suppliernumbers.length > 0) {
    metaData.push({
      key: '_rza_supplier_numbers',
      value: JSON.stringify(article.suppliernumbers)
    });
  }

  // Add custom fields
  article.fields
    .filter(field => field.value && field.value.trim() !== '')
    .forEach(field => {
      metaData.push({
        key: `_rza_field_${field.fieldnumber}`,
        value: field.value
      });
    });

  // Add textile article information if present
  if (article.textilartikel?.kennzeichen) {
    metaData.push({
      key: '_rza_textile_info',
      value: JSON.stringify(article.textilartikel)
    });
  }

  return metaData;
}
