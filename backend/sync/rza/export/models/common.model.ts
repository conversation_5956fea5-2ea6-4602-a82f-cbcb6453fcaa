/**
 * Common model types shared across RZA export domains
 */

/**
 * Generic field for custom data
 */
export interface RzaField {
  fieldnumber: string;
  value: string;
}

/**
 * Tax rate configuration for different countries
 */
export interface RzaTaxRate {
  countryID: number;
  tax: number;
}

/**
 * Discount configuration with date range and price group
 */
export interface RzaDiscount {
  fromDate: string;
  toDate: string;
  percent: number;
  pricegroup: string;
}

/**
 * Country information
 */
export interface RzaCountry {
  ID: number;
  name: string;
  ISO: string;
}

/**
 * Translation for multi-language support
 */
export interface RzaTranslation {
  languageId: number;
  name: string;
  longdescription: string;
}

/**
 * Price configuration with quantity breaks
 */
export interface RzaPrice {
  price: number;
  percent?: string;
  pricegroup: string;
  from: number;
}
