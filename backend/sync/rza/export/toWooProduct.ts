import { Product } from '@/services/woocommerce';
import { RzaArticle, RzaCategory, RzaPrice } from '@/sync/rza/export/types';

/**
 * Maps a product from the RZA system to a WooCommerce product.
 *
 * @param rzaArticle - The RZA article to convert
 * @param categories - Available categories for mapping
 * @param priceGroup - The price group to use for pricing (defaults to 'VK-Preis')
 * @returns WooCommerce Product object
 */
export function mapRzaProductToWooProduct(
  rzaArticle: RzaArticle,
  categories: RzaCategory[] = [],
  priceGroup: string = 'VK-Preis'
): Product {
  // Find the primary price for the specified price group
  const primaryPrice = findBestPrice(rzaArticle.prices, priceGroup, 1);

  // Find sale price (if there are quantity discounts)
  const salePrice = findBestPrice(rzaArticle.prices, priceGroup, 10); // Check for bulk pricing

  // Map categories
  const wooCategories = rzaArticle.categories
    .map(categoryId => {
      const category = categories.find(cat => cat.ID === categoryId);
      return category ? { id: category.ID, name: category.name } : { id: categoryId };
    })
    .filter(Boolean);

  // Determine stock status
  const stockStatus = rzaArticle.instock > 0 ? 'instock' : 'outofstock';

  // Determine product status
  const status = determineProductStatus(rzaArticle);

  // Get tax configuration
  const taxConfig = mapTaxConfiguration(rzaArticle);

  // Create the WooCommerce product
  const wooProduct: Product = {
    name: rzaArticle.name,
    slug: generateSlug(rzaArticle.ordernumber),
    type: 'simple',
    status: status,
    featured: false,
    catalog_visibility: 'visible',
    description: rzaArticle.description_long,
    short_description: '', // RZA doesn't seem to have a short description
    sku: rzaArticle.ordernumber,
    price: primaryPrice?.price.toString() || '0',
    regular_price: primaryPrice?.price.toString() || '0',
    manage_stock: true,
    stock_quantity: rzaArticle.instock,
    stock_status: stockStatus,
    categories: wooCategories,
    weight: rzaArticle.weight.toString(),
    tax_status: taxConfig.tax_status,
    tax_class: taxConfig.tax_class,
    // Add custom meta data for RZA specific fields
    meta_data: [
      { key: '_rza_artikel_id', value: rzaArticle.rzaArtikelID.toString() },
      { key: '_rza_art_group_id', value: rzaArticle.artGroupID.toString() },
      { key: '_rza_art_sub_group_id', value: rzaArticle.artSubGroupID.toString() },
      { key: '_rza_unit_id', value: rzaArticle.unitID },
      { key: '_rza_ean', value: rzaArticle.ean },
      { key: '_rza_barcode', value: rzaArticle.barcode },
      { key: '_rza_tax_rate', value: rzaArticle.tax.toString() },
      { key: '_rza_shipping_time', value: rzaArticle.shippingtime },
      { key: '_rza_supplier_numbers', value: JSON.stringify(rzaArticle.suppliernumbers) },
      { key: '_rza_stock', value: rzaArticle.stock.toString() },
      // Add custom fields
      ...rzaArticle.fields
        .filter(field => field.value && field.value.trim() !== '')
        .map(field => ({
          key: `_rza_field_${field.fieldnumber}`,
          value: field.value
        }))
    ]
  };

  // Add sale price if applicable
  if (salePrice && salePrice.price < (primaryPrice?.price || 0)) {
    wooProduct.sale_price = salePrice.price.toString();
  }

  return wooProduct;
}

/**
 * Maps multiple RZA articles to WooCommerce products
 *
 * @param rzaArticles - Array of RZA articles to convert
 * @param categories - Available categories for mapping
 * @param priceGroup - The price group to use for pricing (defaults to 'VK-Preis')
 * @returns Array of WooCommerce Product objects
 */
export function mapRzaProductsToWooProducts(
  rzaArticles: RzaArticle[],
  categories: RzaCategory[] = [],
  priceGroup: string = 'VK-Preis'
): Product[] {
  return rzaArticles.map(article =>
    mapRzaProductToWooProduct(article, categories, priceGroup)
  );
}

/**
 * Finds the best price for a given price group and quantity
 *
 * @param prices - Array of RZA prices
 * @param priceGroup - The price group to search for
 * @param quantity - The quantity to find the best price for
 * @returns The best price for the given quantity, or undefined if not found
 */
export function findBestPrice(
  prices: RzaPrice[],
  priceGroup: string,
  quantity: number = 1
): RzaPrice | undefined {
  const groupPrices = prices
    .filter(price => price.pricegroup === priceGroup)
    .sort((a, b) => b.from - a.from); // Sort by quantity descending

  return groupPrices.find(price => quantity >= price.from);
}

/**
 * Generates a WooCommerce-compatible slug from a string
 *
 * @param text - The text to convert to a slug
 * @returns A URL-safe slug
 */
export function generateSlug(text: string): string {
  return text
    .toLowerCase()
    .replace(/[^a-z0-9\s-]/g, '') // Remove special characters
    .replace(/\s+/g, '-') // Replace spaces with hyphens
    .replace(/-+/g, '-') // Replace multiple hyphens with single
    .replace(/^-|-$/g, ''); // Remove leading/trailing hyphens
}

/**
 * Determines if a product should be published based on RZA flags
 *
 * @param rzaArticle - The RZA article to check
 * @returns 'publish' if the product should be visible, 'draft' otherwise
 */
export function determineProductStatus(rzaArticle: RzaArticle): 'publish' | 'draft' {
  return rzaArticle.active === 1 && rzaArticle.onlineshopstatus === 1 ? 'publish' : 'draft';
}

/**
 * Maps RZA tax rates to WooCommerce tax configuration
 *
 * @param rzaArticle - The RZA article with tax information
 * @param countryId - The country ID to get tax rate for (optional)
 * @returns Tax configuration object
 */
export function mapTaxConfiguration(
  rzaArticle: RzaArticle,
  countryId?: number
): { tax_status: string; tax_class: string; tax_rate?: number } {
  let taxRate = rzaArticle.tax;

  // If a specific country is requested, try to find its tax rate
  if (countryId && rzaArticle.taxRates.length > 0) {
    const countryTax = rzaArticle.taxRates.find(rate => rate.countryID === countryId);
    if (countryTax) {
      taxRate = countryTax.tax;
    }
  }

  return {
    tax_status: 'taxable',
    tax_class: '', // Standard tax class
    tax_rate: taxRate
  };
}