# RZA Export System

This directory contains the RZA export functionality for converting RZA ERP data to WooCommerce format.

## 🏗️ Architecture

The system has been restructured for better maintainability and follows domain separation principles:

### Directory Structure

```
backend/sync/rza/export/
├── models/                    # TypeScript model definitions
│   ├── common.model.ts       # Shared types (fields, prices, etc.)
│   ├── article.model.ts      # Article/product models
│   ├── customer.model.ts     # Customer models
│   ├── articleGroup.model.ts # Article group models
│   └── category.model.ts     # Category models
├── mappers/                  # Domain-specific mapping functions
│   ├── articles.mapper.ts    # Article to WooCommerce mapping
│   ├── customers.mapper.ts   # Customer to WooCommerce mapping
│   └── articleGroups.mapper.ts # Category/group mapping
├── exportService.ts          # Main orchestration service
├── index.ts                  # Main export file
├── testExport.ts            # Test demonstrations
├── types.ts                 # Legacy types (deprecated)
├── toWooProduct.ts          # Legacy mapper (deprecated)
└── full_export.example.xml  # Example RZA export XML
```

## 🚀 Quick Start

### Basic Usage

```typescript
import {
  mapRzaArticleToWooProduct,
  mapRzaCustomerToWooCustomer,
  mapRzaCategoriesToWooCategories
} from '@/sync/rza/export';

// Map a single article
const wooProduct = mapRzaArticleToWooProduct(rzaArticle, categories, {
  defaultPriceGroup: 'VK-Preis',
  includeInactiveProducts: false
});

// Map a customer
const wooCustomer = mapRzaCustomerToWooCustomer(rzaCustomer, {
  defaultRole: 'customer',
  createUsername: true
});

// Map categories with hierarchy
const wooCategories = mapRzaCategoriesToWooCategories(rzaCategories, {
  createHierarchy: true,
  slugPrefix: 'rza-'
});
```

### Using the Export Service

```typescript
import { RzaExportService } from '@/sync/rza/export';

const exportService = new RzaExportService({
  xmlFilePath: './data/rza_export.xml',
  outputDirectory: './tmp/rza-export',
  exportArticles: true,
  exportCustomers: true,
  exportCategories: true,
  generateJsonFiles: true
});

const result = await exportService.executeExport();
console.log(`Processed ${result.stats.articlesProcessed} articles`);
```

## 📋 Features

### Article Mapping
- ✅ Complete product information mapping
- ✅ Multi-tier pricing with quantity breaks
- ✅ Tax rate handling per country
- ✅ Stock management and availability
- ✅ Custom fields preservation
- ✅ Textile article variants support
- ✅ Multi-language translations
- ✅ Category assignments
- ✅ Supplier information

### Customer Mapping
- ✅ Complete customer profile mapping
- ✅ Billing and shipping addresses
- ✅ Price group assignments
- ✅ Special price agreements
- ✅ Credit limits and delivery blocks
- ✅ VAT number handling
- ✅ Custom customer fields
- ✅ Username generation
- ✅ Country code mapping

### Category Mapping
- ✅ Hierarchical category structure
- ✅ Article group to category mapping
- ✅ Sub-group handling
- ✅ Discount information inclusion
- ✅ SEO-friendly slug generation
- ✅ Menu ordering

## 🔧 Configuration

### Article Mapping Configuration

```typescript
interface ArticleMappingConfig {
  defaultPriceGroup: string;        // Default price group (e.g., 'VK-Preis')
  defaultLanguageId: number;        // Default language for translations
  includeInactiveProducts: boolean; // Include inactive products
  stockThreshold: number;           // Minimum stock for 'instock' status
  defaultTaxClass: string;          // Default WooCommerce tax class
}
```

### Customer Mapping Configuration

```typescript
interface CustomerMappingConfig {
  defaultRole: string;              // Default WooCommerce user role
  createUsername: boolean;          // Auto-generate usernames
  sendWelcomeEmail: boolean;        // Send welcome emails
  syncBillingAddress: boolean;      // Sync billing address
  syncShippingAddress: boolean;     // Sync shipping address
  defaultCountryCode: string;       // Default country code
}
```

### Category Mapping Configuration

```typescript
interface CategoryMappingConfig {
  createHierarchy: boolean;         // Maintain parent-child relationships
  includeDiscounts: boolean;        // Include discount information
  defaultDisplay: string;           // Default category display type
  slugPrefix: string;               // Prefix for category slugs
}
```

## 📊 Data Mapping Details

### Article Field Mapping

| RZA Field | WooCommerce Field | Notes |
|-----------|-------------------|-------|
| `name` | `name` | Direct mapping |
| `ordernumber` | `sku` | Used as product SKU |
| `ordernumber` | `slug` | Converted to URL-safe slug |
| `description_long` | `description` | HTML content preserved |
| `instock` | `stock_quantity` | Available quantity |
| `weight` | `weight` | Direct mapping |
| `active` + `onlineshopstatus` | `status` | Combined logic for publish/draft |
| `categories` | `categories` | Mapped using category lookup |

### Price Mapping

RZA supports complex pricing with quantity breaks:
- Finds best price for specified price group
- Supports quantity-based discounts
- Maps to WooCommerce regular_price and sale_price

### Meta Data Preservation

RZA-specific data is preserved in WooCommerce meta fields:

- `_rza_artikel_id` - RZA article ID
- `_rza_art_group_id` - Article group ID
- `_rza_art_sub_group_id` - Article sub-group ID
- `_rza_unit_id` - Unit of measurement
- `_rza_ean` - EAN barcode
- `_rza_barcode` - Additional barcode
- `_rza_tax_rate` - Tax rate
- `_rza_shipping_time` - Delivery time
- `_rza_supplier_numbers` - Supplier part numbers
- `_rza_stock` - Actual stock level
- `_rza_field_X` - Custom fields (X = field number)
- `_rza_textile_info` - Textile article information

## 🧪 Testing

Run the test suite to verify functionality:

```bash
# Run TypeScript compilation test
npx tsc --noEmit backend/sync/rza/export/testExport.ts

# Run the test demonstrations
node -r ts-node/register backend/sync/rza/export/testExport.ts
```

## 🔄 Migration from Legacy Code

The old structure has been deprecated but remains for backward compatibility:

### Old Usage (Deprecated)
```typescript
import { mapRzaProductToWooProduct } from './toWooProduct';
import { RzaArticle } from './types';
```

### New Usage (Recommended)
```typescript
import { mapRzaArticleToWooProduct, RzaArticle } from './index';
// or
import { mapRzaArticleToWooProduct } from './mappers/articles.mapper';
import { RzaArticle } from './models/article.model';
```

## 📝 Best Practices

1. **Use Type-Safe Imports**: Import specific types and functions
2. **Configure Mappings**: Use configuration objects for customization
3. **Handle Errors**: Wrap mapping calls in try-catch blocks
4. **Validate Data**: Check for required fields before mapping
5. **Test Thoroughly**: Use the test functions to verify mappings
6. **Preserve Metadata**: Keep RZA-specific data in meta fields
7. **Monitor Performance**: Use batch processing for large datasets

## 🤝 Contributing

When adding new features:

1. Add types to appropriate model files
2. Create mapping functions in domain-specific mappers
3. Update configuration interfaces
4. Add tests to testExport.ts
5. Update this README

## 📚 Related Documentation

- [WooCommerce REST API Documentation](https://woocommerce.github.io/woocommerce-rest-api-docs/)
- [RZA ERP System Documentation](https://rza.at/)
- [TypeScript Handbook](https://www.typescriptlang.org/docs/)
- **Comprehensive Coverage**: All XML elements mapped to TypeScript interfaces
- **Extensible**: Easy to extend for additional RZA fields

## Mapping Functions

### Core Functions

#### `mapRzaProductToWooProduct()`

Converts a single RZA article to a WooCommerce product.

```typescript
function mapRzaProductToWooProduct(
  rzaArticle: RzaArticle,
  categories: RzaCategory[] = [],
  priceGroup: string = 'VK-Preis'
): Product
```

**Parameters:**
- `rzaArticle` - The RZA article to convert
- `categories` - Available categories for mapping (optional)
- `priceGroup` - Price group to use for pricing (default: 'VK-Preis')

**Returns:** WooCommerce `Product` object

#### `mapRzaProductsToWooProducts()`

Converts multiple RZA articles to WooCommerce products.

```typescript
function mapRzaProductsToWooProducts(
  rzaArticles: RzaArticle[],
  categories: RzaCategory[] = [],
  priceGroup: string = 'VK-Preis'
): Product[]
```

### Utility Functions

#### `findBestPrice()`

Finds the best price for a given quantity and price group.

```typescript
function findBestPrice(
  prices: RzaPrice[],
  priceGroup: string,
  quantity: number = 1
): RzaPrice | undefined
```

#### `generateSlug()`

Generates a WooCommerce-compatible URL slug.

```typescript
function generateSlug(text: string): string
```

#### `determineProductStatus()`

Determines if a product should be published based on RZA flags.

```typescript
function determineProductStatus(rzaArticle: RzaArticle): 'publish' | 'draft'
```

#### `mapTaxConfiguration()`

Maps RZA tax information to WooCommerce tax configuration.

```typescript
function mapTaxConfiguration(
  rzaArticle: RzaArticle,
  countryId?: number
): { tax_status: string; tax_class: string; tax_rate?: number }
```

## Mapping Details

### Product Fields Mapping

| RZA Field | WooCommerce Field | Notes |
|-----------|-------------------|-------|
| `name` | `name` | Direct mapping |
| `ordernumber` | `sku` | Used as product SKU |
| `ordernumber` | `slug` | Converted to URL-safe slug |
| `description_long` | `description` | HTML content preserved |
| `instock` | `stock_quantity` | Available quantity |
| `weight` | `weight` | Direct mapping |
| `active` + `onlineshopstatus` | `status` | Combined logic for publish/draft |
| `categories` | `categories` | Mapped using category lookup |

### Price Mapping

The system supports complex pricing with quantity breaks:

1. **Primary Price**: Found using `priceGroup` with `from: 1`
2. **Sale Price**: Found using quantity breaks (e.g., `from: 10`)
3. **Price Groups**: Support for different customer price groups

### Meta Data

RZA-specific data is preserved in WooCommerce meta fields:

- `_rza_artikel_id` - RZA article ID
- `_rza_art_group_id` - Article group ID
- `_rza_art_sub_group_id` - Article sub-group ID
- `_rza_unit_id` - Unit of measurement
- `_rza_ean` - EAN code
- `_rza_barcode` - Barcode
- `_rza_tax_rate` - Tax rate
- `_rza_shipping_time` - Shipping time
- `_rza_supplier_numbers` - Supplier numbers (JSON)
- `_rza_stock` - Actual stock level
- `_rza_field_X` - Custom fields (where X is field number)

### Tax Handling

- Default tax rate from `rzaArticle.tax`
- Country-specific tax rates from `rzaArticle.taxRates`
- Automatic tax status set to 'taxable'

## Usage Examples

### Basic Usage

```typescript
import { mapRzaProductToWooProduct, RzaArticle } from './toWooProduct';

const rzaArticle: RzaArticle = {
  // ... article data
};

const wooProduct = mapRzaProductToWooProduct(rzaArticle);
```

### With Categories

```typescript
const categories = [
  { ID: 271, name: 'Garden Tools' }
];

const wooProduct = mapRzaProductToWooProduct(
  rzaArticle, 
  categories, 
  'VK-Preis'
);
```

### Bulk Conversion

```typescript
const wooProducts = mapRzaProductsToWooProducts(
  rzaArticles,
  categories,
  'VK-DE-Preis'
);
```

## Error Handling

The mapping functions are designed to be robust:

- Missing prices default to '0'
- Missing categories are handled gracefully
- Invalid data is filtered out
- Optional fields are handled properly

## Integration

This module integrates with:

- **WooCommerce Service** (`../../services/woocommerce.ts`)
- **Product Interface** - Uses the extended `Product` interface
- **Sync System** - Part of the broader sync infrastructure

## Testing

See `example.ts` for comprehensive usage examples and test cases.
