# RZA to WooCommerce Product Mapping

This module provides TypeScript types and mapping functions to convert products from the RZA ERP system to WooCommerce products.

## Files

- `toWooProduct.ts` - Main types and mapping functions
- `full_export.example.xml` - Example XML export from RZA system

## TypeScript Types

### RZA Types

The module defines comprehensive TypeScript interfaces for all RZA export data structures:

- **`RzaArticle`** - Main product/article interface
- **`RzaPrice`** - Price information with quantity breaks
- **`RzaTaxRate`** - Tax rates per country
- **`RzaTranslation`** - Multi-language support
- **`RzaField`** - Custom fields
- **`RzaTextilartikel`** - Textile-specific attributes
- **`RzaCategory`** - Product categories
- **`RzaCustomer`** - Customer data
- **`RzaOrder`** - Order information
- **`RzaExport`** - Complete export structure

### Key Features

- **Type Safety**: Full TypeScript support with strict typing
- **Comprehensive Coverage**: All XML elements mapped to TypeScript interfaces
- **Extensible**: Easy to extend for additional RZA fields

## Mapping Functions

### Core Functions

#### `mapRzaProductToWooProduct()`

Converts a single RZA article to a WooCommerce product.

```typescript
function mapRzaProductToWooProduct(
  rzaArticle: RzaArticle,
  categories: RzaCategory[] = [],
  priceGroup: string = 'VK-Preis'
): Product
```

**Parameters:**
- `rzaArticle` - The RZA article to convert
- `categories` - Available categories for mapping (optional)
- `priceGroup` - Price group to use for pricing (default: 'VK-Preis')

**Returns:** WooCommerce `Product` object

#### `mapRzaProductsToWooProducts()`

Converts multiple RZA articles to WooCommerce products.

```typescript
function mapRzaProductsToWooProducts(
  rzaArticles: RzaArticle[],
  categories: RzaCategory[] = [],
  priceGroup: string = 'VK-Preis'
): Product[]
```

### Utility Functions

#### `findBestPrice()`

Finds the best price for a given quantity and price group.

```typescript
function findBestPrice(
  prices: RzaPrice[],
  priceGroup: string,
  quantity: number = 1
): RzaPrice | undefined
```

#### `generateSlug()`

Generates a WooCommerce-compatible URL slug.

```typescript
function generateSlug(text: string): string
```

#### `determineProductStatus()`

Determines if a product should be published based on RZA flags.

```typescript
function determineProductStatus(rzaArticle: RzaArticle): 'publish' | 'draft'
```

#### `mapTaxConfiguration()`

Maps RZA tax information to WooCommerce tax configuration.

```typescript
function mapTaxConfiguration(
  rzaArticle: RzaArticle,
  countryId?: number
): { tax_status: string; tax_class: string; tax_rate?: number }
```

## Mapping Details

### Product Fields Mapping

| RZA Field | WooCommerce Field | Notes |
|-----------|-------------------|-------|
| `name` | `name` | Direct mapping |
| `ordernumber` | `sku` | Used as product SKU |
| `ordernumber` | `slug` | Converted to URL-safe slug |
| `description_long` | `description` | HTML content preserved |
| `instock` | `stock_quantity` | Available quantity |
| `weight` | `weight` | Direct mapping |
| `active` + `onlineshopstatus` | `status` | Combined logic for publish/draft |
| `categories` | `categories` | Mapped using category lookup |

### Price Mapping

The system supports complex pricing with quantity breaks:

1. **Primary Price**: Found using `priceGroup` with `from: 1`
2. **Sale Price**: Found using quantity breaks (e.g., `from: 10`)
3. **Price Groups**: Support for different customer price groups

### Meta Data

RZA-specific data is preserved in WooCommerce meta fields:

- `_rza_artikel_id` - RZA article ID
- `_rza_art_group_id` - Article group ID
- `_rza_art_sub_group_id` - Article sub-group ID
- `_rza_unit_id` - Unit of measurement
- `_rza_ean` - EAN code
- `_rza_barcode` - Barcode
- `_rza_tax_rate` - Tax rate
- `_rza_shipping_time` - Shipping time
- `_rza_supplier_numbers` - Supplier numbers (JSON)
- `_rza_stock` - Actual stock level
- `_rza_field_X` - Custom fields (where X is field number)

### Tax Handling

- Default tax rate from `rzaArticle.tax`
- Country-specific tax rates from `rzaArticle.taxRates`
- Automatic tax status set to 'taxable'

## Usage Examples

### Basic Usage

```typescript
import { mapRzaProductToWooProduct, RzaArticle } from './toWooProduct';

const rzaArticle: RzaArticle = {
  // ... article data
};

const wooProduct = mapRzaProductToWooProduct(rzaArticle);
```

### With Categories

```typescript
const categories = [
  { ID: 271, name: 'Garden Tools' }
];

const wooProduct = mapRzaProductToWooProduct(
  rzaArticle, 
  categories, 
  'VK-Preis'
);
```

### Bulk Conversion

```typescript
const wooProducts = mapRzaProductsToWooProducts(
  rzaArticles,
  categories,
  'VK-DE-Preis'
);
```

## Error Handling

The mapping functions are designed to be robust:

- Missing prices default to '0'
- Missing categories are handled gracefully
- Invalid data is filtered out
- Optional fields are handled properly

## Integration

This module integrates with:

- **WooCommerce Service** (`../../services/woocommerce.ts`)
- **Product Interface** - Uses the extended `Product` interface
- **Sync System** - Part of the broader sync infrastructure

## Testing

See `example.ts` for comprehensive usage examples and test cases.
