# RZA Import System

This system handles the conversion of WooCommerce orders to RZA ERP import XML files. It creates new customers and transactions in the RZA system based on WooCommerce order data.

## Architecture

The system is organized into separate modules for better maintainability:

- **Models** (`models/`): TypeScript interfaces and types
  - `common.model.ts`: Shared types and transaction structure
  - `document.model.ts`: Document and position-related types
  - `address.model.ts`: Address and customer-related types
- **Mappers** (`mappers/`): Domain-specific mapping functions
  - `documents.mapper.ts`: WooCommerce orders to RZA documents
  - `addresses.mapper.ts`: WooCommerce customers to RZA addresses
- **Services**: XML generation and orchestration
  - `importService.ts`: Main service orchestrating the import process
  - `xmlGenerator.ts`: XML generation from RZA data structures
- **Configuration**: Flexible mapping configuration for different use cases

### Key Features

- **Complete Type Safety**: Full TypeScript support for all RZA XML elements
- **Domain Separation**: Clean separation between document and address mapping
- **Flexible Configuration**: Configurable mapping behavior and validation rules
- **Validation**: Comprehensive validation for addresses, documents, and configuration
- **File Management**: Automatic filename generation and temporary file storage
- **Error Handling**: Detailed error reporting and validation messages
- **Country Mapping**: Comprehensive mapping from ISO codes to German country names

## Field Mapping Documentation

### Document Mapping (WooCommerce Order → RZA Document)

| WooCommerce Field | RZA Field | Type | Notes |
|-------------------|-----------|------|-------|
| `order.id` | `Dokument.RefID` | number | Unique document reference |
| `order.date_created` | `Dokument.Datum` | string | Converted to DD.MM.YYYY format |
| `order.total` | `Summen.EndsummeBrutto` | number | Total order amount including tax |
| `order.total_tax` | `Summen.EndsummeMWSt` | number | Total tax amount |
| `order.total - order.total_tax` | `Summen.EndsummeNetto` | number | Net amount (calculated) |
| `order.currency` | `Dokument.Waehrung` | string | Currency code (EUR, USD, etc.) |

#### Line Items Mapping (WooCommerce Line Items → RZA Positions)

| WooCommerce Field | RZA Field | Type | Notes |
|-------------------|-----------|------|-------|
| `item.id` | `Position.PositionInterneInformationen.ID` | number | Internal position ID |
| `item.product_id` | `Position.PositionArtikelinfo.ArtikelID` | number | Product reference |
| `item.sku` | `Position.PositionArtikelinfo.Artikelnummer` | string | Product SKU or product_id as fallback |
| `item.name` | `Position.Positionstext.Textzeile[0].Text` | string | Product name |
| `item.quantity` | `Position.Menge` | number | Quantity ordered |
| `item.price` | `Position.Preis` | number | Unit price |
| `item.total` | `Position.Betrag` | number | Line total (price × quantity) |
| `item.taxes[0]` | `Position.MWSt` | number | Tax rate percentage (calculated) |

**Why these mappings:**
- **RefID**: Uses WooCommerce order ID for traceability
- **Date format**: RZA requires DD.MM.YYYY format instead of ISO dates
- **Tax calculation**: RZA expects percentage rates, calculated from tax amounts
- **Currency**: Direct mapping for international orders
- **Line items**: Each WooCommerce line item becomes an RZA position

### Address Mapping (WooCommerce Customer → RZA Address)

| WooCommerce Field | RZA Field | Type | Notes |
|-------------------|-----------|------|-------|
| `billing.last_name` | `Adresse.Zuname` | string | Customer surname |
| `billing.first_name` | `Adresse.Vorname` | string | Customer first name |
| `billing.company` | `Adresse.Firma` | string | Company name (optional) |
| `billing.address_1` | `Anschrift.Strasse` | string | Street address |
| `billing.address_2` | `Anschrift.Strasse` | string | Combined with address_1 if enabled |
| `billing.postcode` | `Anschrift.PLZ` | string | Postal code |
| `billing.city` | `Anschrift.Ort` | string | City name |
| `billing.country` | `Anschrift.Land` | string | Country name (mapped from ISO codes) |
| `billing.email` | `Kontakt.Email` | string | Email address |
| `billing.phone` | `Kontakt.Telefon1` | string | Phone number (formatted) |

#### Shipping Address Mapping (Alternative Delivery Address)

When shipping address differs from billing:

| WooCommerce Field | RZA Field | Type | Notes |
|-------------------|-----------|------|-------|
| `shipping.first_name + last_name` | `AbweichendeLieferanschrift.Textzeile[0]` | string | Full name |
| `shipping.company` | `AbweichendeLieferanschrift.Textzeile[1]` | string | Company (if present) |
| `shipping.address_1` | `AbweichendeLieferanschrift.Textzeile[2]` | string | Street address |
| `shipping.address_2` | `AbweichendeLieferanschrift.Textzeile[3]` | string | Additional address |
| `shipping.postcode + city` | `AbweichendeLieferanschrift.Textzeile[4]` | string | Combined postal code and city |
| `shipping.country` | `AbweichendeLieferanschrift.Textzeile[5]` | string | Country (if different from AT) |

**Why these mappings:**
- **Name fields**: Direct mapping for customer identification
- **Address combination**: RZA allows combining address lines for better formatting
- **Country mapping**: Converts ISO 2-letter codes to German country names
- **Phone formatting**: Removes spaces/hyphens for consistent format
- **Alternative delivery**: Uses text lines for flexible address formatting

### Country Code Mapping

The system includes comprehensive country code mapping from WooCommerce ISO codes to German country names:

| ISO Code | German Name | ISO Code | German Name |
|----------|-------------|----------|-------------|
| AT | Österreich | DE | Deutschland |
| CH | Schweiz | IT | Italien |
| FR | Frankreich | ES | Spanien |
| NL | Niederlande | BE | Belgien |
| PL | Polen | CZ | Tschechien |
| HU | Ungarn | SK | Slowakei |
| SI | Slowenien | HR | Kroatien |
| DK | Dänemark | SE | Schweden |
| NO | Norwegen | FI | Finnland |
| GB | Großbritannien | IE | Irland |

**Why German names:** RZA system expects localized country names for proper processing.

## Configuration Options

### Document Configuration (`DocumentMappingConfig`)

```typescript
{
  documentType: 'Auftrag',           // Document type in RZA
  defaultUnit: 'Stk.',              // Default unit for positions
  defaultTaxRate: 20,               // Default tax rate (%)
  defaultErlösekonto: '4020',       // Default revenue account
  defaultArtikelgruppe: '01',       // Default article group
  includeTaxInPrice: true,          // Whether prices include tax
  calculateMaterialkosten: true,    // Calculate material costs
  materialkostenPercentage: 60      // Material cost percentage
}
```

### Address Configuration (`AddressMappingConfig`)

```typescript
{
  alwaysCreateNewCustomer: true,    // Always create new customer (ID = '0')
  includeShippingAddress: true,     // Include alternative delivery address
  combineAddressLines: true,        // Combine address_1 and address_2
  defaultWaehrungID: 21,           // Default currency ID (EUR)
  defaultFiliallagerID: 1,         // Default branch/warehouse ID
  mapCompanyToFirma: true,         // Map company field
  mapPhoneToTelefon1: true,        // Map phone to Telefon1
  requireEmail: true,              // Email is required
  requireName: true,               // Name is required
  requireAddress: true             // Address is required
}
```

## Usage Examples

### Basic Import

```typescript
import { RzaImportService } from './importService';
import { createDefaultDocumentConfig, createDefaultAddressConfig } from './index';

const service = new RzaImportService(wooCommerceService, {
  tmpDirectory: '/tmp/rza-imports',
  rzaConfig: {
    currency: 'EUR',
    belegkreisID: 1,
    filiallagerID: 1,
    waehrungID: 21,
    verarbeitung: 'ECHT'
  },
  documentConfig: createDefaultDocumentConfig(),
  addressConfig: createDefaultAddressConfig()
});

// Import specific orders
const result = await service.importSpecificOrders([12345, 12346]);
```

### Custom Configuration

```typescript
const customDocumentConfig = createDefaultDocumentConfig({
  documentType: 'Rechnung',
  defaultTaxRate: 19,
  calculateMaterialkosten: false
});

const customAddressConfig = createDefaultAddressConfig({
  alwaysCreateNewCustomer: false,
  combineAddressLines: false,
  requireEmail: false
});
```

## File Output

Generated XML files are saved in the configured `tmpDirectory` with the naming pattern:
```
rza-import-order-{ORDER_ID}-{TIMESTAMP}.xml
```

Example: `rza-import-order-12345-2024-01-15T10-30-00.xml`

## Error Handling

The system provides comprehensive error handling and validation:

- **Configuration validation**: Checks required fields and formats
- **Address validation**: Validates required address fields and email format
- **Document validation**: Ensures all required document fields are present
- **XML generation**: Validates data before XML creation

All errors are logged and returned in the `ImportResult` structure for proper error handling.

## Best Practices

1. **Always validate configuration** before processing orders
2. **Use batch processing** for multiple orders to improve performance
3. **Monitor the tmp directory** for generated files
4. **Implement proper error handling** for failed imports
5. **Test with small batches** before processing large order volumes
6. **Keep configuration consistent** across different environments

## XML Structure Example

Based on the real `import.example.xml` file, the generated XML follows this structure:

```xml
<?xml version="1.0" encoding="UTF-8"?>
<Transaktion RefID="12345" Modus="KOMPLETT" Verarbeitung="ECHT"
             Zeitstempel="03.07.2025 10:30">

  <Dokument Art="Auftrag" RefID="12345" AdresseRefID="112345"
            Datum="03.07.2025" Waehrung="EUR" Belegnummer="12345">

    <Summen EndsummeNetto="108.33" EndsummeMWSt="21.66" EndsummeBrutto="129.99"/>

    <DokumentInterneInformationen BelegkreisID="1" FiliallagerID="1" WaehrungID="21"/>

    <Positionen>
      <Position LfdNummer="1" Menge="2" Preis="49.99" Einheit="Stk." MWSt="20">
        <PositionArtikelinfo ArtikelID="123" Artikelnummer="SKU-001"
                           Artikelgruppe="01" Untergruppe="001"/>
        <Positionstext>
          <Textzeile Zeile="1" Text="Product Name"/>
        </Positionstext>
        <PositionInterneInformationen ID="1" Erlösekonto="4020"
                                    Materialkosten="60.00"/>
      </Position>
    </Positionen>

  </Dokument>

  <Adresse RefID="112345" ID="0" Zuname="Mustermann" Vorname="Max"
           Firma="Example GmbH">
    <Anschrift Strasse="Musterstraße 123" PLZ="1010" Ort="Wien" Land="Österreich"/>
    <Kontakt Email="<EMAIL>" Telefon1="+43123456789"/>
    <AdresseInterneInformationen WaehrungID="21" FiliallagerID="1"/>
  </Adresse>

</Transaktion>
```

## Integration Notes

- **File Transport**: Generated XML files need to be transported to the RZA system (implementation not included)
- **Scheduling**: Consider implementing automated scheduling for regular imports
- **Monitoring**: Add logging and monitoring for import processes
- **Error Recovery**: Implement retry mechanisms for failed imports
- **Performance**: System is optimized for individual order processing
