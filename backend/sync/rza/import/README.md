# RZA Import System

This module handles the conversion of WooCommerce orders to RZA import XML format for integration with the RZA ERP system.

## Overview

The RZA import system fetches new orders from WooCommerce (from a given timestamp onwards) and generates XML files in the RZA import format. Each order creates a new customer with a new address as requested.

## Architecture

### Core Components

1. **Types (`types.ts`)** - Complete TypeScript type definitions for RZA import XML structure
2. **Mapper (`mapper.ts`)** - Functions to convert WooCommerce orders to RZA format
3. **XML Generator (`xmlGenerator.ts`)** - Generates XML from RZA data structures
4. **Import Service (`importService.ts`)** - Orchestrates the complete import process
5. **Test Files** - Comprehensive testing and examples

### Key Features

- **Complete Type Safety**: Full TypeScript support for all RZA XML elements
- **Flexible Configuration**: Configurable sender information, document types, and processing modes
- **Validation**: Input validation for both configuration and generated data
- **File Management**: Automatic filename generation and temporary file storage
- **Error Handling**: Comprehensive error reporting and validation
- **Batch Processing**: Support for single orders or bulk processing

## Usage

### Basic Usage

```typescript
import { RzaImportService } from './importService';
import { WooCommerceService } from '../../../services/woocommerce';
import { createDefaultRzaConfig } from './mapper';

// Initialize services
const wooCommerceService = new WooCommerceService();
const config = RzaImportService.createDefaultConfig({
  tmpDirectory: '/tmp/rza-imports',
  rzaConfig: createDefaultRzaConfig({
    senderName: 'Your Company GmbH',
    senderStreet: 'Your Street 123',
    senderPLZ: '1010',
    senderCity: 'Vienna',
    senderCountry: 'AT',
    senderUID: 'ATU12345678',
    documentType: 'Rechnung',
    verarbeitung: 'ECHT' // or 'TEST'
  })
});

const importService = new RzaImportService(wooCommerceService, config);

// Import new orders from a specific timestamp
const fromDate = new Date('2025-07-01T00:00:00Z');
const result = await importService.importNewOrders(fromDate);

if (result.success) {
  console.log(`Generated XML files for orders: ${result.orderIds.join(', ')}`);
  console.log(`Files saved to: ${result.filepath}`);
} else {
  console.error('Import failed:', result.errors);
}
```

### Import Specific Orders

```typescript
// Import specific orders by ID
const orderIds = [12345, 12346, 12347];
const result = await importService.importSpecificOrders(orderIds);
```

### Configuration

#### RZA Import Configuration

```typescript
interface RzaImportConfig {
  // Sender information (required)
  senderName: string;
  senderStreet?: string;
  senderPLZ?: string;
  senderCity?: string;
  senderCountry?: string;
  senderUID?: string;
  
  // Document settings (required)
  documentType: DokumentArt; // 'Rechnung', 'Auftrag', etc.
  currency: string; // 'EUR', 'USD', etc.
  belegkreisID: number;
  filiallagerID: number;
  waehrungID: number;
  
  // Processing settings
  verarbeitung: 'TEST' | 'ECHT';
  erstelltVon?: string;
  
  // Default values
  defaultUnit?: string;
  defaultTaxRate?: number;
}
```

#### Service Configuration

```typescript
interface ImportServiceConfig {
  tmpDirectory: string; // Directory for generated XML files
  rzaConfig: RzaImportConfig;
}
```

## Data Mapping

### WooCommerce Order → RZA Document

| WooCommerce Field | RZA Field | Notes |
|-------------------|-----------|-------|
| `id` | `RefID` | Used as document reference ID |
| `date_created` | `Datum` | Converted to YYYY-MM-DD format |
| `currency` | `Waehrung` | Direct mapping |
| `total` | `Bruttosumme` | Total amount including tax |
| `line_items` | `Positionen` | Each item becomes a position |

### WooCommerce Billing → RZA Address

| WooCommerce Field | RZA Field | Notes |
|-------------------|-----------|-------|
| `first_name` | `Vorname` | Customer first name |
| `last_name` | `Zuname` | Customer last name |
| `company` | `Firma` | Company name (optional) |
| `address_1` + `address_2` | `Strasse` | Combined street address |
| `postcode` | `PLZ` | Postal code |
| `city` | `Ort` | City name |
| `country` | `Land` | Country code |
| `phone` | `Telefon` | Phone number |
| `email` | `Email` | Email address |

### Line Items → Positions

| WooCommerce Field | RZA Field | Notes |
|-------------------|-----------|-------|
| `sku` | `Artikelnummer` | Product SKU |
| `name` | `Bezeichnung` | Product name |
| `quantity` | `Menge` | Quantity ordered |
| `price` | `Preis` | Unit price |

## XML Structure

The generated XML follows the RZA import schema:

```xml
<?xml version="1.0" encoding="UTF-8"?>
<rza:Transaktion xmlns:rza="https://www.rza.at/XML/Fakt/" 
                 xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                 xsi:schemaLocation="https://www.rza.at/XML/Fakt/ Transaktion.xsd"
                 Erstellungsdatum="2025-07-03T10:30:00Z"
                 Modus="KOMPLETT"
                 Verarbeitung="ECHT"
                 ErstelltVon="WooCommerce-Sync">
  
  <rza:Absender Name="Company Name" Strasse="Street" PLZ="1010" Ort="Vienna" Land="AT"/>
  
  <rza:Dokument Art="Rechnung" RefID="12345" AdresseRefID="22345" 
                Datum="2025-07-03" Waehrung="EUR" Belegnummer="12345">
    <rza:Summen Nettosumme="108.33" Steuersumme="21.66" Bruttosumme="129.99"/>
    <rza:DokumentInternetInformationen BelegkreisID="10" FiliallagerID="1" WaehrungID="1"/>
    <rza:Positionen>
      <rza:Position LfdNummer="1" Menge="2" Preis="49.99" Einheit="Stk">
        <rza:PositionArtikelinfo Artikelnummer="SKU-001" Bezeichnung="Product Name"/>
      </rza:Position>
    </rza:Positionen>
  </rza:Dokument>
  
  <rza:Adresse RefID="22345" ID="NULL" Zuname="Lastname" Vorname="Firstname"
               Strasse="Customer Street" PLZ="1020" Ort="Vienna" Land="AT"
               Email="<EMAIL>"/>
  
</rza:Transaktion>
```

## File Management

### Filename Generation

Files are automatically named using the pattern:
- Single order: `rza_import_order_{ORDER_ID}_{YYYYMMDD}_{HHMMSS}.xml`
- Multiple orders: `rza_import_orders_{COUNT}_{YYYYMMDD}_{HHMMSS}.xml`

### Temporary Directory

XML files are saved to the configured temporary directory. The directory is created automatically if it doesn't exist.

## Error Handling

The system provides comprehensive error handling:

### Configuration Validation
- Validates required fields in RZA configuration
- Checks for valid numeric IDs
- Ensures required sender information is present

### Data Validation
- Validates generated RZA data structure
- Checks for required XML elements
- Ensures proper data types and formats

### Runtime Errors
- WooCommerce API connection issues
- File system errors
- XML generation errors

## Testing

### Running Tests

```bash
# Compile and run standalone test
npx tsc sync/rza/import/testStandalone.ts --outDir ./dist --target ES2022 --module commonjs --esModuleInterop
node dist/testStandalone.js
```

### Test Coverage

The test suite covers:
- Order mapping functionality
- XML generation
- Configuration validation
- Data validation
- Filename generation
- Complete workflow testing

## Integration

### With Sync System

The RZA import service integrates with the existing sync system:

```typescript
// In your sync service
import { RzaImportService } from './sync/rza/import/importService';

async function syncOutboundOrders() {
  const lastSyncTime = await getLastSyncTimestamp();
  const result = await importService.importNewOrders(lastSyncTime);
  
  if (result.success) {
    await updateLastSyncTimestamp(new Date());
    // Handle file transport to RZA server (out of scope)
  }
}
```

### Environment Configuration

```env
# RZA Import Configuration
RZA_SENDER_NAME="Your Company GmbH"
RZA_SENDER_STREET="Your Street 123"
RZA_SENDER_PLZ="1010"
RZA_SENDER_CITY="Vienna"
RZA_SENDER_COUNTRY="AT"
RZA_SENDER_UID="ATU12345678"
RZA_BELEGKREIS_ID=10
RZA_FILIALLAGER_ID=1
RZA_WAEHRUNG_ID=1
RZA_VERARBEITUNG="ECHT"
RZA_TMP_DIRECTORY="/tmp/rza-imports"
```

## Next Steps

1. **File Transport**: Implement file transfer to RZA server (SFTP, API, etc.)
2. **Scheduling**: Add automated scheduling for regular imports
3. **Monitoring**: Add logging and monitoring for import processes
4. **Error Recovery**: Implement retry mechanisms for failed imports
5. **Performance**: Optimize for large order volumes
