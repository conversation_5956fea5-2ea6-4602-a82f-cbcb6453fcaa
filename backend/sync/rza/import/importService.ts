/**
 * RZA Import Service
 * Orchestrates the process of fetching WooCommerce orders and generating RZA import XML files
 */

import * as fs from 'fs/promises';
import * as path from 'path';
import { createModuleLogger } from '../../../infrastructure/logger';
import { WooCommerceService } from '../../../services/woocommerce';
import {
  RzaImportConfig,
  RzaImportData,
  WooCommerceOrderExtended
} from './types';
import {
  mapWooOrderToRzaImport,
  mapWooOrdersToRzaImports,
  createDefaultRzaConfig,
  validateRzaConfig,
  generateImportFilename
} from './mapper';
import {
  generateRzaImportXML,
  validateRzaImportData
} from './xmlGenerator';

const logger = createModuleLogger('rza-import');

export interface ImportServiceConfig {
  tmpDirectory: string;
  rzaConfig: RzaImportConfig;
}

export interface ImportResult {
  success: boolean;
  filename?: string;
  filepath?: string;
  orderIds: number[];
  errors: string[];
  xmlContent?: string;
}

export class RzaImportService {
  private wooCommerceService: WooCommerceService;
  private config: ImportServiceConfig;

  constructor(wooCommerceService: WooCommerceService, config: ImportServiceConfig) {
    this.wooCommerceService = wooCommerceService;
    this.config = config;
  }

  /**
   * Fetches new orders from WooCommerce and generates RZA import XML
   * 
   * @param fromTimestamp - Fetch orders created after this timestamp
   * @param orderStatuses - Array of order statuses to include (default: ['processing', 'completed'])
   * @returns Import result with file information
   */
  async importNewOrders(
    fromTimestamp: Date,
    orderStatuses: string[] = ['processing', 'completed']
  ): Promise<ImportResult> {
    try {
      logger.info('Starting RZA import for new orders', {
        fromTimestamp: fromTimestamp.toISOString(),
        orderStatuses
      });

      // Validate configuration
      const configErrors = validateRzaConfig(this.config.rzaConfig);
      if (configErrors.length > 0) {
        return {
          success: false,
          orderIds: [],
          errors: [`Configuration errors: ${configErrors.join(', ')}`]
        };
      }

      // Fetch orders from WooCommerce
      const orders = await this.fetchOrdersFromWooCommerce(fromTimestamp, orderStatuses);
      
      if (orders.length === 0) {
        logger.info('No new orders found for import');
        return {
          success: true,
          orderIds: [],
          errors: [],
          xmlContent: ''
        };
      }

      logger.info(`Found ${orders.length} orders for import`, {
        orderIds: orders.map(o => o.id)
      });

      // Generate import data for all orders
      const importDataList = mapWooOrdersToRzaImports(orders, this.config.rzaConfig);

      // Process each order separately (as requested - one XML per order)
      const results: ImportResult[] = [];
      
      for (const importData of importDataList) {
        const result = await this.generateSingleOrderXML(importData, orders);
        results.push(result);
      }

      // Return combined result
      const allOrderIds = results.flatMap(r => r.orderIds);
      const allErrors = results.flatMap(r => r.errors);
      const successfulResults = results.filter(r => r.success);

      return {
        success: successfulResults.length === results.length,
        orderIds: allOrderIds,
        errors: allErrors,
        filename: results.length === 1 ? results[0].filename : `${results.length}_files_generated`,
        filepath: results.length === 1 ? results[0].filepath : this.config.tmpDirectory
      };

    } catch (error) {
      logger.error('Failed to import orders:', error);
      return {
        success: false,
        orderIds: [],
        errors: [error instanceof Error ? error.message : 'Unknown error occurred']
      };
    }
  }

  /**
   * Imports specific orders by their IDs
   * 
   * @param orderIds - Array of WooCommerce order IDs to import
   * @returns Import result with file information
   */
  async importSpecificOrders(orderIds: number[]): Promise<ImportResult> {
    try {
      logger.info('Starting RZA import for specific orders', { orderIds });

      // Validate configuration
      const configErrors = validateRzaConfig(this.config.rzaConfig);
      if (configErrors.length > 0) {
        return {
          success: false,
          orderIds: [],
          errors: [`Configuration errors: ${configErrors.join(', ')}`]
        };
      }

      // Fetch specific orders from WooCommerce
      const orders: WooCommerceOrderExtended[] = [];
      const fetchErrors: string[] = [];

      for (const orderId of orderIds) {
        try {
          const order = await this.wooCommerceService.getOrder(orderId) as WooCommerceOrderExtended;
          orders.push(order);
        } catch (error) {
          fetchErrors.push(`Failed to fetch order ${orderId}: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
      }

      if (orders.length === 0) {
        return {
          success: false,
          orderIds: [],
          errors: ['No orders could be fetched', ...fetchErrors]
        };
      }

      // Generate import data
      const importDataList = mapWooOrdersToRzaImports(orders, this.config.rzaConfig);

      // Process each order separately
      const results: ImportResult[] = [];
      
      for (const importData of importDataList) {
        const result = await this.generateSingleOrderXML(importData, orders);
        results.push(result);
      }

      // Return combined result
      const allOrderIds = results.flatMap(r => r.orderIds);
      const allErrors = [...fetchErrors, ...results.flatMap(r => r.errors)];
      const successfulResults = results.filter(r => r.success);

      return {
        success: successfulResults.length === results.length && fetchErrors.length === 0,
        orderIds: allOrderIds,
        errors: allErrors,
        filename: results.length === 1 ? results[0].filename : `${results.length}_files_generated`,
        filepath: results.length === 1 ? results[0].filepath : this.config.tmpDirectory
      };

    } catch (error) {
      logger.error('Failed to import specific orders:', error);
      return {
        success: false,
        orderIds: [],
        errors: [error instanceof Error ? error.message : 'Unknown error occurred']
      };
    }
  }

  /**
   * Fetches orders from WooCommerce based on timestamp and status
   * 
   * @param fromTimestamp - Fetch orders created after this timestamp
   * @param orderStatuses - Array of order statuses to include
   * @returns Array of WooCommerce orders
   */
  private async fetchOrdersFromWooCommerce(
    fromTimestamp: Date,
    orderStatuses: string[]
  ): Promise<WooCommerceOrderExtended[]> {
    const params = {
      after: fromTimestamp.toISOString(),
      status: orderStatuses.join(','),
      per_page: 100, // Adjust as needed
      orderby: 'date',
      order: 'asc'
    };

    const orders = await this.wooCommerceService.getOrders(params) as WooCommerceOrderExtended[];
    return orders;
  }

  /**
   * Generates XML file for a single order
   * 
   * @param importData - RZA import data
   * @param allOrders - All orders being processed (for context)
   * @returns Import result for single order
   */
  private async generateSingleOrderXML(
    importData: RzaImportData,
    allOrders: WooCommerceOrderExtended[]
  ): Promise<ImportResult> {
    try {
      // Find the order this import data corresponds to
      const orderId = importData.transaktion.Dokument[0].RefID;
      const order = allOrders.find(o => o.id === orderId);
      
      if (!order) {
        return {
          success: false,
          orderIds: [orderId],
          errors: [`Order ${orderId} not found in order list`]
        };
      }

      // Validate import data
      const validationErrors = validateRzaImportData(importData);
      if (validationErrors.length > 0) {
        return {
          success: false,
          orderIds: [orderId],
          errors: [`Validation errors for order ${orderId}: ${validationErrors.join(', ')}`]
        };
      }

      // Generate XML
      const xmlContent = generateRzaImportXML(importData);

      // Generate filename
      const filename = generateImportFilename([orderId]);
      const filepath = path.join(this.config.tmpDirectory, filename);

      // Ensure tmp directory exists
      await fs.mkdir(this.config.tmpDirectory, { recursive: true });

      // Write XML file
      await fs.writeFile(filepath, xmlContent, 'utf8');

      logger.info(`Generated RZA import XML for order ${orderId}`, {
        filename,
        filepath,
        fileSize: xmlContent.length
      });

      return {
        success: true,
        filename,
        filepath,
        orderIds: [orderId],
        errors: [],
        xmlContent
      };

    } catch (error) {
      const orderId = importData.transaktion.Dokument[0]?.RefID || 0;
      logger.error(`Failed to generate XML for order ${orderId}:`, error);
      
      return {
        success: false,
        orderIds: [orderId],
        errors: [error instanceof Error ? error.message : 'Unknown error occurred']
      };
    }
  }

  /**
   * Creates a default import service configuration
   * 
   * @param overrides - Configuration overrides
   * @returns Default import service configuration
   */
  static createDefaultConfig(overrides: Partial<ImportServiceConfig> = {}): ImportServiceConfig {
    return {
      tmpDirectory: path.join(process.cwd(), 'tmp', 'rza-imports'),
      rzaConfig: createDefaultRzaConfig(),
      ...overrides
    };
  }

  /**
   * Updates the RZA configuration
   * 
   * @param newConfig - New RZA configuration
   */
  updateRzaConfig(newConfig: Partial<RzaImportConfig>): void {
    this.config.rzaConfig = { ...this.config.rzaConfig, ...newConfig };
  }

  /**
   * Gets the current configuration
   * 
   * @returns Current import service configuration
   */
  getConfig(): ImportServiceConfig {
    return { ...this.config };
  }
}
