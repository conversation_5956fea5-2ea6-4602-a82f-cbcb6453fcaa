/**
 * Document mapper for RZA import system
 * Maps WooCommerce orders to RZA documents (invoices)
 */

import {
  RzaDokument,
  RzaPosition,
  RzaPositionArtikelinfo,
  RzaPositionstext,
  RzaPositionInterneInformationen,
  RzaSummen,
  RzaDokumentInterneInformationen,
  WooCommerceOrder,
  DocumentMappingConfig
} from '../models/document.model';
import { RzaImportConfig, RzaTextzeile } from '../models/common.model';

/**
 * Maps a WooCommerce order to an RZA document
 * 
 * @param order - WooCommerce order to convert
 * @param config - RZA import configuration
 * @param documentConfig - Document-specific configuration
 * @param addressRefID - Reference ID for the associated address
 * @returns RZA document structure
 */
export function mapWooOrderToRzaDocument(
  order: WooCommerceOrder,
  config: RzaImportConfig,
  documentConfig: DocumentMappingConfig,
  addressRefID: number
): RzaDokument {
  const documentRefID = order.id;
  
  // Format date from WooCommerce to RZA format (DD.MM.YYYY)
  const orderDate = formatDateForRza(order.date_created);
  
  // Map order positions
  const positions = mapOrderLineItems(order, documentConfig);
  
  // Calculate document sums
  const sums = calculateDocumentSums(order);
  
  // Create document internal information
  const interneInformationen: RzaDokumentInterneInformationen = {
    ID: 0, // Always 0 for new documents
    BelegkreisID: config.belegkreisID,
    FiliallagerID: config.filiallagerID,
    BenutzernamenID: config.benutzernamenID || 2,
    WaehrungID: config.waehrungID
  };
  
  const document: RzaDokument = {
    RefID: documentRefID,
    AdresseRefID: addressRefID,
    Art: documentConfig.documentType,
    Datum: orderDate,
    Waehrung: config.currency,
    Summen: sums,
    Zahlungskondition: {
      Zieltage: 30 // Default payment terms, could be configurable
    },
    DokumentInterneInformationen: interneInformationen,
    Positionen: {
      Position: positions
    }
  };
  
  return document;
}

/**
 * Maps WooCommerce line items to RZA positions
 * 
 * @param order - WooCommerce order
 * @param config - Document mapping configuration
 * @returns Array of RZA positions
 */
export function mapOrderLineItems(
  order: WooCommerceOrder,
  config: DocumentMappingConfig
): RzaPosition[] {
  return order.line_items.map((item, index) => {
    // Create article information
    const articleInfo: RzaPositionArtikelinfo = {
      ArtikelID: item.product_id,
      Artikelgruppe: config.defaultArtikelgruppe,
      Untergruppe: '', // Could be mapped from product categories
      Artikelnummer: item.sku || item.product_id.toString(),
      EAN: extractEANFromMeta(item)
    };
    
    // Create position text
    const positionstext: RzaPositionstext = {
      Textzeile: [
        {
          Zeile: 1,
          Text: item.name
        }
      ]
    };
    
    // Calculate material costs if enabled
    let materialkosten: number | undefined;
    if (config.calculateMaterialkosten) {
      const itemTotal = parseFloat(item.total);
      materialkosten = itemTotal * (config.materialkostenPercentage / 100);
    }
    
    // Create position internal information
    const interneInformationen: RzaPositionInterneInformationen = {
      ID: item.id,
      Erloesekonto: config.defaultErlösekonto,
      Materialkosten: materialkosten
    };
    
    // Calculate tax rate from item
    const taxRate = calculateItemTaxRate(item);
    
    // Calculate position amount
    const preis = item.price;
    const menge = item.quantity;
    const betrag = preis * menge;
    
    const position: RzaPosition = {
      LfdNummer: index + 1,
      Menge: menge,
      Preis: preis,
      Betrag: betrag,
      MWSt: taxRate,
      Einheit: config.defaultUnit,
      Rabattfaehig: 'JA',
      PositionArtikelinfo: articleInfo,
      Positionstext: positionstext,
      PositionInterneInformationen: interneInformationen
    };
    
    return position;
  });
}

/**
 * Calculates document sums from WooCommerce order
 * 
 * @param order - WooCommerce order
 * @returns RZA sums structure
 */
export function calculateDocumentSums(order: WooCommerceOrder): RzaSummen {
  const bruttosumme = parseFloat(order.total);
  const steuersumme = parseFloat(order.total_tax);
  const nettosumme = bruttosumme - steuersumme;
  
  return {
    EndsummeNetto: nettosumme,
    EndsummeMWSt: steuersumme,
    EndsummeBrutto: bruttosumme
  };
}

/**
 * Extracts EAN from product meta data
 * 
 * @param item - WooCommerce line item
 * @returns EAN string or undefined
 */
export function extractEANFromMeta(item: WooCommerceOrder['line_items'][0]): string | undefined {
  if (!item.meta_data) return undefined;
  
  const eanKeys = ['_ean', 'ean', 'barcode', 'gtin', '_gtin'];
  const eanMeta = item.meta_data.find(meta => 
    eanKeys.includes(meta.key.toLowerCase())
  );
  
  return eanMeta?.value;
}

/**
 * Calculates tax rate for a line item
 * 
 * @param item - WooCommerce line item
 * @returns Tax rate as percentage
 */
export function calculateItemTaxRate(item: WooCommerceOrder['line_items'][0]): number {
  if (!item.taxes || item.taxes.length === 0) {
    return 0;
  }
  
  // Calculate tax rate from first tax entry
  const tax = item.taxes[0];
  const subtotal = parseFloat(item.subtotal);
  const taxAmount = parseFloat(tax.total);
  
  if (subtotal === 0) return 0;
  
  return Math.round((taxAmount / subtotal) * 100);
}

/**
 * Formats a date string to RZA format (DD.MM.YYYY)
 * 
 * @param dateString - ISO date string from WooCommerce
 * @returns Formatted date string
 */
export function formatDateForRza(dateString: string): string {
  const date = new Date(dateString);
  const day = date.getDate().toString().padStart(2, '0');
  const month = (date.getMonth() + 1).toString().padStart(2, '0');
  const year = date.getFullYear();
  
  return `${day}.${month}.${year}`;
}

/**
 * Formats a datetime string to RZA format (DD.MM.YYYY HH:mm)
 * 
 * @param dateString - ISO datetime string
 * @returns Formatted datetime string
 */
export function formatDateTimeForRza(dateString: string): string {
  const date = new Date(dateString);
  const day = date.getDate().toString().padStart(2, '0');
  const month = (date.getMonth() + 1).toString().padStart(2, '0');
  const year = date.getFullYear();
  const hours = date.getHours().toString().padStart(2, '0');
  const minutes = date.getMinutes().toString().padStart(2, '0');
  
  return `${day}.${month}.${year} ${hours}:${minutes}`;
}

/**
 * Creates default document mapping configuration
 * 
 * @param overrides - Configuration overrides
 * @returns Default document mapping configuration
 */
export function createDefaultDocumentConfig(
  overrides: Partial<DocumentMappingConfig> = {}
): DocumentMappingConfig {
  return {
    documentType: 'Auftrag',
    defaultUnit: 'Stk.',
    defaultTaxRate: 20,
    defaultErlösekonto: '4020',
    defaultArtikelgruppe: '01',
    includeTaxInPrice: true,
    includeProductImages: false,
    includeProductMeta: true,
    calculateMaterialkosten: true,
    materialkostenPercentage: 60,
    ...overrides
  };
}

/**
 * Validates document data before XML generation
 *
 * @param document - RZA document to validate
 * @returns Array of validation errors
 */
export function validateRzaDocument(document: RzaDokument): string[] {
  const errors: string[] = [];

  if (!document.RefID || document.RefID <= 0) {
    errors.push('Document RefID is required and must be positive');
  }

  if (!document.AdresseRefID || document.AdresseRefID <= 0) {
    errors.push('Address RefID is required and must be positive');
  }

  if (!document.Datum || !document.Datum.match(/^\d{2}\.\d{2}\.\d{4}$/)) {
    errors.push('Document date must be in DD.MM.YYYY format');
  }

  if (!document.Waehrung || document.Waehrung.trim() === '') {
    errors.push('Currency is required');
  }

  if (!document.Positionen || !document.Positionen.Position || document.Positionen.Position.length === 0) {
    errors.push('At least one position is required');
  }

  // Validate positions
  document.Positionen?.Position?.forEach((position, index) => {
    if (!position.LfdNummer || position.LfdNummer <= 0) {
      errors.push(`Position ${index + 1}: LfdNummer is required and must be positive`);
    }

    if (!position.Menge || position.Menge <= 0) {
      errors.push(`Position ${index + 1}: Menge is required and must be positive`);
    }

    if (!position.PositionArtikelinfo) {
      errors.push(`Position ${index + 1}: PositionArtikelinfo is required`);
    }
  });

  return errors;
}

/**
 * Extracts discount information from WooCommerce order
 *
 * @param order - WooCommerce order
 * @returns Discount percentage or undefined
 */
export function extractDiscountFromOrder(order: WooCommerceOrder): number | undefined {
  const discountTotal = parseFloat(order.discount_total);
  if (discountTotal <= 0) return undefined;

  const subtotal = order.line_items.reduce((sum, item) => {
    return sum + parseFloat(item.subtotal);
  }, 0);

  if (subtotal <= 0) return undefined;

  return Math.round((discountTotal / subtotal) * 100);
}
