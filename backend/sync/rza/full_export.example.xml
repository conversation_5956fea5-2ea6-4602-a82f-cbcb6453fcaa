<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<root> <!-- beliebiger gültiger Knotenname //-->
    <articles>
        <article>
            <ordernumber>GRASS-HOPPER</ordernumber> <!-- Entspricht RZA Artikelstammdaten Maske Feld "Nummer" //-->
            <rzaArtikelID>774</rzaArtikelID> <!-- Auszug aus "Muster Import Einfach" <rza:PositionArtikelinfo ArtikelID="774"/> //-->			
			<artGroupID>1</artGroupID> <!-- ID  - Artikelgruppe//-->
			<artSubGroupID>1</artSubGroupID> <!-- ID - Untergruppe  //-->			
            <!-- <supplier>Green Hippo</supplier> Entspricht RZA Artikelstammdaten Art.-Gruppe -> "Bezeichnung" nicht die "Nr."  ENFÄLLT  //-->
            <categories>
                <category>271</category> <!-- Entspricht RZA Artikelstammdaten Button "Erweitert" im Reiter "Online-Shop" bei "Produktgruppen" -> Die Id übergeben nicht den Text //-->
            </categories>
            <name>Green Hippo Gras Hoper</name> <!-- Produktname //-->
            <ean>0</ean> 
			<barcode></barcode> <!-- Strichcode in fakt //-->
            <unitID>stk</unitID>
            <instock>0</instock> <!-- Verfügbare Menge wird ausgegeben - nicht Lagerstand //-->
			<stock>0</stock> <!-- tatsächlicher Lagerstand //-->
            <weight>0.000</weight> 
            <active>1</active> <!-- Entspricht RZA Artikelstammdaten Button "Erweitert" im Reiter "Online-Shop" => "Artikel in Online Shop stellen" //-->
            <description_long><![CDATA[<p>Tuslibet inhospitalitas. Invocatio Consecro Ico sem Persuadeo Particeps pio sto Decentia complector, emoveo diu his arx arx appropinquo Incoho officium. Quid, ubi caedes, inferi sapor tam Convinco rex miraculose, ut quo Quidne oro sanctimonialis Fervesco subsidarius, edo incomposite Diffinio cupio vae ferramentum, nam caedo Mensa voro deprecatio par per laqueus Promo Marcieniensis quicumque adula.</p>]]></description_long>
            <tax>20.00</tax> <!-- Steuer //-->
            <taxRates> <!--Sachkonten pro Land inkl. Steuersatz - es werden hier nur jene angeführt, welche das Hakerl "Land kann in Online-Shop ausgewählt werden" aktiviert haben -->
				<taxRate>
					<countryID>1</countryID>  <!-- Land ID aus Einstellungen Allgemein -->					
					<tax>19.00</tax> <!-- Steuersatz -->
				</taxRate>
				<taxRate>
					<countryID>2</countryID>					
					<tax>17.00</tax>
				</taxRate>		 
			</taxRates>
			<suppliernumbers> <!--NEU: Herstellernummern: Es werden alle ausgegeben--> 
				<suppliernumber>Test1</suppliernumber>
				<suppliernumber>Test2</suppliernumber>
			</suppliernumbers>  <!--FRÜHER	Lieferanten bzw. Hersteller Artkelnummer (sofern vorhanden) //-->
            <shippingtime></shippingtime> <!-- Entspricht RZA Artikelstammdaten Reiter "Lieferant" Feld "Lieferzeit". Wenn mehrere Lieferanten vorhanden sind wird nur bei einem Lieferanten die Lieferzeit vom Kunden gepflegt. //-->
            <fields>  <!--Frei Definierbare Felder: Es werden alle ausgegeben (maximal 10)--> 
			  <field fieldnumber="1">Test1</field>
			  <field fieldnumber="2"></field>	
			  <field fieldnumber="3"></field>	
			  <field fieldnumber="4"></field>	
			  <field fieldnumber="5"></field>	
			  <field fieldnumber="6"></field>	
			  <field fieldnumber="7"></field>	
			  <field fieldnumber="8"></field>	
			  <field fieldnumber="9"></field>	
			  <field fieldnumber="10"></field>	
			</fields>
            <translations>
                <translation>
                    <languageId>1</languageId>
                    <name>Green Hippo Gras Hoper - english</name>
                    <longdescription><![CDATA[<p>Eo Opera Ora ullus Decolor, Caleo. Evolo cui patior, creptio quo debeo sus perspicax. Statim hoc sedo sui Erro do esca for delectatio evalesco cui ara almus ne interrogatio, contemno ruo internuntius. Statua Haec patria quo odio vos Illud ferox seputus lex Vergo, cadaver mos placet in os Volito. Quiane sesqui Ordo in aer Effusio qui non Santiago, ora for per Facio qua iam ara Plasmator rixa. Natu.</p>]]></longdescription>
                </translation>
            </translations>
            <prices>			
                <price>
                    <price>10.75</price>
                    <percent></percent>
                    <pricegroup>VK-Preis</pricegroup>
                    <from>1</from>
                </price>
                <price>
                    <price>10.75</price>
                    <percent></percent>
                    <pricegroup>VK-DE-Preis</pricegroup>
                    <from>1</from>
                </price>
                <price>
                    <price>9.10</price>
                    <percent></percent>
                    <pricegroup>VK-Preis 1</pricegroup>
                    <from>1</from>
                </price>
                <price>
                    <price>9.10</price>
                    <percent></percent>
                    <pricegroup>VK-Preis 6</pricegroup>
                    <from>1</from>
                </price>
                <price>
                    <price>8.84</price>
                    <percent></percent>
                    <pricegroup>VK-Preis 1</pricegroup>
                    <from>10</from>
                </price>
                <price>
                    <price>8.58</price>
                    <percent></percent>
                    <pricegroup>VK-Preis 1</pricegroup>
                    <from>48</from>
                </price>
            </prices>
			<textilartikel> <!-- Kommt nur zum Tragen, wenn es sich um einen Textilartikel handelt -->
				<kennzeichen></kennzeichen> <!-- 1 = Hauptartikel 2= Textilartikel -->
				<textilartikelID></textilartikelID> <!-- referenziert auf den Hauptartikel (ID) -->
				<groessentabelleID></groessentabelleID> 
				<groessenID></groessenID> 
				<farbtabelleID></farbtabelleID> 
				<farbenID></farbenID> 
				<saisonID></saisonID> 
				<preiszuschlag></preiszuschlag> 
			</textilartikel>	
			<onlineshopstatus>1</onlineshopstatus> <!-- Standardwert = 1 für Online Shop Artikel - kommt nur 0 wenn man in den Artikelstammdaten das Hackerl Online-Shop Artikel entfernt und nur bei diesem einen File -->
        </article>
    </articles>
    <customers>
        <customer>
            <rzaAddressId></rzaAddressId> <!-- Dantenbank ID im RZA, eindeutige Kundenidentifikation //-->
            <customernumber></customernumber> <!-- Shop Kundennummer wenn vorhanden (Info: wird bei Order.xml bei Adresse im Attribut "KundennummerShop" eingefügt) //-->         
			<pricegroup>Standard VK</pricegroup> <!--> Es werden hier immer alle ausgegeben - Gegensatz zu den Artikel//-->    
			<fields>  <!--> frei def. Felder des Kontaktes//-->   
				<field fieldnumber="1"></field>
				<field fieldnumber="2"></field>
				<field fieldnumber="3"></field>
				<field fieldnumber="4"></field>
				<field fieldnumber="5"></field>
			</fields>			        
			<totaldiscount>
				<discount></discount>
				<from></from>
				<to></to>
			</totaldiscount>
		 <Anrede></Anrede>
         <Titel></Titel>
         <Zuname></Zuname>
         <Vorname></Vorname>
         <Namenszeile2></Namenszeile2>
         <Namenszeile3></Namenszeile3>
         <Land></Land>
         <PLZ></PLZ>
         <Ort></Ort>
         <Strasse></Strasse>
         <UID></UID>
         <Mail></Mail>
         <Telefon1></Telefon1>
         <Telefon2></Telefon2>
         <Fax></Fax>
         <Kreditlimit>0</Kreditlimit>
         <Liefersperre>0</Liefersperre>
		 <countryID>22</countryID> <!-- Eindeutige ID des Landes/Währung -->
		 <priceagreements> <!--Preisvereinbarungen - gehören extra aktiviert!-->
			<agreement>
				 <rzaArtikelID></rzaArtikelID> <!--Artikel ID!-->
				 <artGroupID></artGroupID> <!--Artikelgruppe ID!-->
				 <artSubGroupID></artSubGroupID> <!--Untergruppe ID!-->			 
				 <discount></discount> <!--Rabatt!-->
				 <price></price> <!--Preis!-->
				 <from></from> <!--Ab Menge!-->
				 <fromDate></fromDate> <!--Von Datum!-->
				 <toDate></toDate>	<!--Bis Datum!-->
			</agreement>   
	   </priceagreements>
		</customer>
    </customers>
    <orders>
        <order>
            <ordernumber></ordernumber> <!-- Bestellnummer des Webshops, wird bei Order.xml bei DokumentInterneInformationen im Attribut "Nummer" eingefügt //-->
            <orderstatus></orderstatus>
            <!-- Folgende Stati wären im Webshop vorhanden (Zahlen sind die Webshop IDs)            
                5 Komplett ausgeliefert
                6 Teilweise ausgeliefert
                7 Rechnung erstellt            
            //-->
        </order>
    </orders>	
	<groupDefinition>  <!-- Artikelgruppen + Untergruppen inkl. Aktionen //-->
		<artGroups>
			<artGroup>
				<ID>1</ID>
				<number>1</number>
				<name>Test</name>		
				<discoutns>
					<discount>					
					  <fromDate>10.03.2018</fromDate>
					  <toDate>31.03.2018</toDate>
					  <percent>5.00</percent>
					  <pricegroup>VK-Preis</pricegroup>	 <!--> Es werden hier immer alle ausgegeben - Gegensatz zu den Artikel//-->       				
					</discount>
					<discount>					
					  <fromDate>01.04.2018</fromDate>
					  <toDate>10.04.2018</toDate>
					  <percent>10.00</percent>
					  <pricegroup>VK-Preis</pricegroup>					
					</discount>
				</discoutns>				
			</artGroup>		
			<artGroup>
				<ID>2</ID>
				<number>2</number>
				<name>Test Neu</name>	
				<discoutns>
					<discount>					
					  <fromDate>10.03.2018</fromDate>
					  <toDate>31.03.2018</toDate>
					  <percent>5.00</percent>
					  <pricegroup>VK-Preis</pricegroup>					
					</discount>
					<discount>					
					  <fromDate>01.04.2018</fromDate>
					  <toDate>10.04.2018</toDate>
					  <percent>10.00</percent>
					  <pricegroup>VK-Preis</pricegroup>					
					</discount>
				</discoutns>					
			</artGroup>		
		</artGroups>
		<artSubGroups>
			<artSubGroup>
				<ID>1</ID>
				<number>100</number>
				<name>Test Untergruppe</name>		
				<discoutns>
					<discount>					
					  <fromDate>10.03.2018</fromDate>
					  <toDate>31.03.2018</toDate>
					  <percent>5.00</percent>
					  <pricegroup>VK-Preis</pricegroup>					
					</discount>					
				</discoutns>					
			</artSubGroup>		
			<artSubGroup>
				<ID>2</ID>
				<number>200</number>
				<name>Test Untergruppe 2</name>	
			</artSubGroup>		
		</artSubGroups>
	</groupDefinition>
	<categories>  <!--> Es werden hier immer alle Produktgruppen ausgegeben//-->   
		<category>
			<ID> </ID>
			<ParentID> </ParentID>
			<name> </name>
		</category>	
	</categories>
	<countries> <!-- Es werden alle Länder angeführt, welche das Hakerl "Land kann in Online-Shop ausgewählt werden" haben -->
		<country>
			<ID></ID> <!-- eindeutige ID -->
			<name></name> <!-- Bezeichnung des Landes -->
			<ISO></ISO> <!-- ISO Codes des Landes -->
		</country>
	</countries>	
</root>