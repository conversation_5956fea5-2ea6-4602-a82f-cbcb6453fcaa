import { defineStore } from 'pinia'
import axios from 'axios'

const api = axios.create({
  baseURL: '/api',
  timeout: 30000
})

export interface HealthStatus {
  status: string
  timestamp: string
  database: string
  uptime: number
  memory: any
  version: string
}

export interface SyncStatus {
  id?: number
  type: 'products' | 'orders'
  direction: 'inbound' | 'outbound'
  status: 'pending' | 'running' | 'completed' | 'failed'
  started_at: string
  completed_at?: string
  records_processed: number
  records_total: number
  error_message?: string
  metadata?: string
}

export interface CurrentSyncStatus {
  isRunning: boolean
  lastSync?: SyncStatus
}

export const useApiStore = defineStore('api', {
  state: () => ({
    isLoading: false,
    error: null as string | null
  }),

  actions: {
    async getHealth(): Promise<HealthStatus> {
      try {
        this.isLoading = true
        this.error = null
        const response = await api.get('/health')
        return response.data
      } catch (error) {
        this.error = 'Failed to get health status'
        throw error
      } finally {
        this.isLoading = false
      }
    },

    async getSyncStatus(): Promise<CurrentSyncStatus> {
      try {
        this.isLoading = true
        this.error = null
        const response = await api.get('/sync/status')
        return response.data
      } catch (error) {
        this.error = 'Failed to get sync status'
        throw error
      } finally {
        this.isLoading = false
      }
    },

    async getSyncHistory(limit = 50): Promise<SyncStatus[]> {
      try {
        this.isLoading = true
        this.error = null
        const response = await api.get(`/sync/history?limit=${limit}`)
        return response.data
      } catch (error) {
        this.error = 'Failed to get sync history'
        throw error
      } finally {
        this.isLoading = false
      }
    },

    async triggerSync(type: 'products' | 'orders', direction: 'inbound' | 'outbound'): Promise<SyncStatus> {
      try {
        this.isLoading = true
        this.error = null
        const response = await api.post('/sync/trigger', { type, direction })
        return response.data
      } catch (error) {
        this.error = `Failed to trigger ${type} sync`
        throw error
      } finally {
        this.isLoading = false
      }
    },

    async testWooCommerceConnection(): Promise<boolean> {
      try {
        this.isLoading = true
        this.error = null
        const response = await api.get('/woocommerce/test')
        return response.data.connected
      } catch (error) {
        this.error = 'Failed to test WooCommerce connection'
        return false
      } finally {
        this.isLoading = false
      }
    },

    async getProducts(params: any = {}): Promise<any[]> {
      try {
        this.isLoading = true
        this.error = null
        const response = await api.get('/woocommerce/products', { params })
        return response.data
      } catch (error) {
        this.error = 'Failed to get products'
        throw error
      } finally {
        this.isLoading = false
      }
    },

    async getOrders(params: any = {}): Promise<any[]> {
      try {
        this.isLoading = true
        this.error = null
        const response = await api.get('/woocommerce/orders', { params })
        return response.data
      } catch (error) {
        this.error = 'Failed to get orders'
        throw error
      } finally {
        this.isLoading = false
      }
    },

    // Settings methods (to be implemented when backend is ready)
    async saveWooCommerceSettings(settings: any): Promise<void> {
      try {
        this.isLoading = true
        this.error = null
        await api.post('/settings/woocommerce', settings)
      } catch (error) {
        this.error = 'Failed to save WooCommerce settings'
        throw error
      } finally {
        this.isLoading = false
      }
    },

    async saveSyncSettings(settings: any): Promise<void> {
      try {
        this.isLoading = true
        this.error = null
        await api.post('/settings/sync', settings)
      } catch (error) {
        this.error = 'Failed to save sync settings'
        throw error
      } finally {
        this.isLoading = false
      }
    },

    async saveExternalServerSettings(settings: any): Promise<void> {
      try {
        this.isLoading = true
        this.error = null
        await api.post('/settings/external-server', settings)
      } catch (error) {
        this.error = 'Failed to save external server settings'
        throw error
      } finally {
        this.isLoading = false
      }
    }
  }
})
