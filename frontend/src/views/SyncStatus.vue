<template>
  <div class="sync-status">
    <div class="container">
      <h2>Sync Status</h2>
      
      <!-- Current Status -->
      <div class="card">
        <h3>Current Status</h3>
        <div class="current-status">
          <div class="status-item">
            <strong>Is Running:</strong>
            <span class="badge" :class="currentStatus.isRunning ? 'badge-warning' : 'badge-success'">
              {{ currentStatus.isRunning ? 'RUNNING' : 'IDLE' }}
            </span>
          </div>
          
          <div v-if="currentStatus.lastSync" class="status-item">
            <strong>Last Sync:</strong>
            <span>{{ currentStatus.lastSync.type }} ({{ currentStatus.lastSync.direction }})</span>
            <span class="badge" :class="getBadgeClass(currentStatus.lastSync.status)">
              {{ currentStatus.lastSync.status }}
            </span>
          </div>
        </div>
        
        <div class="sync-actions">
          <button @click="triggerProductSync" class="btn btn-success" :disabled="isLoading || currentStatus.isRunning">
            Sync Products (Inbound)
          </button>
          <button @click="triggerOrderSync" class="btn btn-warning" :disabled="isLoading || currentStatus.isRunning">
            Sync Orders (Outbound)
          </button>
          <button @click="refreshStatus" class="btn btn-secondary" :disabled="isLoading">
            Refresh
          </button>
        </div>
      </div>

      <!-- Sync History -->
      <div class="card">
        <h3>Sync History</h3>
        
        <div v-if="syncHistory.length > 0">
          <table class="table">
            <thead>
              <tr>
                <th>ID</th>
                <th>Type</th>
                <th>Direction</th>
                <th>Status</th>
                <th>Started</th>
                <th>Completed</th>
                <th>Records</th>
                <th>Duration</th>
                <th>Error</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="sync in syncHistory" :key="sync.id">
                <td>{{ sync.id }}</td>
                <td>{{ sync.type }}</td>
                <td>{{ sync.direction }}</td>
                <td>
                  <span class="badge" :class="getBadgeClass(sync.status)">
                    {{ sync.status }}
                  </span>
                </td>
                <td>{{ formatDate(sync.started_at) }}</td>
                <td>{{ sync.completed_at ? formatDate(sync.completed_at) : '-' }}</td>
                <td>{{ sync.records_processed }}/{{ sync.records_total }}</td>
                <td>{{ calculateDuration(sync.started_at, sync.completed_at) }}</td>
                <td>
                  <span v-if="sync.error_message" class="error-message" :title="sync.error_message">
                    {{ truncateError(sync.error_message) }}
                  </span>
                  <span v-else>-</span>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
        
        <div v-else-if="!isLoading">
          <p>No sync history available</p>
        </div>
      </div>

      <!-- Loading indicator -->
      <div v-if="isLoading" class="spinner"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useApiStore } from '../stores/api'

const apiStore = useApiStore()

const isLoading = ref(false)
const currentStatus = ref({
  isRunning: false,
  lastSync: null
})
const syncHistory = ref([])

onMounted(async () => {
  await loadSyncData()
})

const loadSyncData = async () => {
  isLoading.value = true
  try {
    // Load current status
    const status = await apiStore.getSyncStatus()
    currentStatus.value = status
    
    // Load sync history
    const history = await apiStore.getSyncHistory(50)
    syncHistory.value = history
    
  } catch (error) {
    console.error('Failed to load sync data:', error)
  } finally {
    isLoading.value = false
  }
}

const refreshStatus = async () => {
  await loadSyncData()
}

const triggerProductSync = async () => {
  isLoading.value = true
  try {
    await apiStore.triggerSync('products', 'inbound')
    await loadSyncData()
  } catch (error) {
    console.error('Failed to trigger product sync:', error)
  } finally {
    isLoading.value = false
  }
}

const triggerOrderSync = async () => {
  isLoading.value = true
  try {
    await apiStore.triggerSync('orders', 'outbound')
    await loadSyncData()
  } catch (error) {
    console.error('Failed to trigger order sync:', error)
  } finally {
    isLoading.value = false
  }
}

const getBadgeClass = (status: string) => {
  switch (status) {
    case 'completed':
      return 'badge-success'
    case 'failed':
      return 'badge-danger'
    case 'running':
      return 'badge-warning'
    case 'pending':
      return 'badge-info'
    default:
      return 'badge-secondary'
  }
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleString()
}

const calculateDuration = (startDate: string, endDate?: string) => {
  if (!endDate) return '-'
  
  const start = new Date(startDate)
  const end = new Date(endDate)
  const duration = end.getTime() - start.getTime()
  
  const seconds = Math.floor(duration / 1000)
  const minutes = Math.floor(seconds / 60)
  const hours = Math.floor(minutes / 60)
  
  if (hours > 0) {
    return `${hours}h ${minutes % 60}m ${seconds % 60}s`
  } else if (minutes > 0) {
    return `${minutes}m ${seconds % 60}s`
  } else {
    return `${seconds}s`
  }
}

const truncateError = (error: string) => {
  return error.length > 50 ? error.substring(0, 50) + '...' : error
}
</script>

<style scoped>
.current-status {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-bottom: 1rem;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.sync-actions {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.error-message {
  color: #dc3545;
  cursor: help;
}

@media (max-width: 768px) {
  .sync-actions {
    flex-direction: column;
  }
  
  .sync-actions .btn {
    width: 100%;
  }
  
  .table {
    font-size: 0.875rem;
  }
}
</style>
