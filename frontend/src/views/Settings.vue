<template>
  <div class="settings">
    <div class="container">
      <h2>Settings</h2>
      
      <div class="card">
        <h3>WooCommerce Configuration</h3>
        <form @submit.prevent="saveWooCommerceSettings">
          <div class="form-group">
            <label class="form-label">WooCommerce URL</label>
            <input 
              v-model="wooCommerceSettings.url" 
              type="url" 
              class="form-control" 
              placeholder="https://your-shop.com"
              required
            >
          </div>
          
          <div class="form-group">
            <label class="form-label">Consumer Key</label>
            <input 
              v-model="wooCommerceSettings.consumerKey" 
              type="text" 
              class="form-control" 
              placeholder="ck_..."
              required
            >
          </div>
          
          <div class="form-group">
            <label class="form-label">Consumer Secret</label>
            <input 
              v-model="wooCommerceSettings.consumerSecret" 
              type="password" 
              class="form-control" 
              placeholder="cs_..."
              required
            >
          </div>
          
          <button type="submit" class="btn btn-success" :disabled="isLoading">
            Save WooCommerce Settings
          </button>
        </form>
      </div>

      <div class="card">
        <h3>Sync Configuration</h3>
        <form @submit.prevent="saveSyncSettings">
          <div class="form-group">
            <label class="form-label">Sync Interval (minutes)</label>
            <input 
              v-model.number="syncSettings.intervalMinutes" 
              type="number" 
              class="form-control" 
              min="1"
              max="1440"
              required
            >
          </div>
          
          <div class="form-group">
            <label class="form-label">
              <input 
                v-model="syncSettings.enableAutoSync" 
                type="checkbox"
              >
              Enable Automatic Sync
            </label>
          </div>
          
          <button type="submit" class="btn btn-success" :disabled="isLoading">
            Save Sync Settings
          </button>
        </form>
      </div>

      <div class="card">
        <h3>External Server Configuration</h3>
        <form @submit.prevent="saveExternalServerSettings">
          <div class="form-group">
            <label class="form-label">Server URL</label>
            <input 
              v-model="externalServerSettings.url" 
              type="url" 
              class="form-control" 
              placeholder="https://your-external-server.com"
              required
            >
          </div>
          
          <div class="form-group">
            <label class="form-label">Username</label>
            <input 
              v-model="externalServerSettings.username" 
              type="text" 
              class="form-control" 
              required
            >
          </div>
          
          <div class="form-group">
            <label class="form-label">Password</label>
            <input 
              v-model="externalServerSettings.password" 
              type="password" 
              class="form-control" 
              required
            >
          </div>
          
          <button type="submit" class="btn btn-success" :disabled="isLoading">
            Save External Server Settings
          </button>
        </form>
      </div>

      <!-- Loading indicator -->
      <div v-if="isLoading" class="spinner"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useApiStore } from '../stores/api'

const apiStore = useApiStore()

const isLoading = ref(false)

const wooCommerceSettings = ref({
  url: '',
  consumerKey: '',
  consumerSecret: ''
})

const syncSettings = ref({
  intervalMinutes: 30,
  enableAutoSync: true
})

const externalServerSettings = ref({
  url: '',
  username: '',
  password: ''
})

onMounted(async () => {
  await loadSettings()
})

const loadSettings = async () => {
  isLoading.value = true
  try {
    // Load current settings from API
    // This would be implemented when the backend settings API is ready
    console.log('Loading settings...')
  } catch (error) {
    console.error('Failed to load settings:', error)
  } finally {
    isLoading.value = false
  }
}

const saveWooCommerceSettings = async () => {
  isLoading.value = true
  try {
    // Save WooCommerce settings via API
    console.log('Saving WooCommerce settings:', wooCommerceSettings.value)
    // await apiStore.saveWooCommerceSettings(wooCommerceSettings.value)
    alert('WooCommerce settings saved successfully!')
  } catch (error) {
    console.error('Failed to save WooCommerce settings:', error)
    alert('Failed to save WooCommerce settings')
  } finally {
    isLoading.value = false
  }
}

const saveSyncSettings = async () => {
  isLoading.value = true
  try {
    // Save sync settings via API
    console.log('Saving sync settings:', syncSettings.value)
    // await apiStore.saveSyncSettings(syncSettings.value)
    alert('Sync settings saved successfully!')
  } catch (error) {
    console.error('Failed to save sync settings:', error)
    alert('Failed to save sync settings')
  } finally {
    isLoading.value = false
  }
}

const saveExternalServerSettings = async () => {
  isLoading.value = true
  try {
    // Save external server settings via API
    console.log('Saving external server settings:', externalServerSettings.value)
    // await apiStore.saveExternalServerSettings(externalServerSettings.value)
    alert('External server settings saved successfully!')
  } catch (error) {
    console.error('Failed to save external server settings:', error)
    alert('Failed to save external server settings')
  } finally {
    isLoading.value = false
  }
}
</script>

<style scoped>
.form-group {
  margin-bottom: 1rem;
}

.form-group label[type="checkbox"] {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.card {
  margin-bottom: 2rem;
}
</style>
