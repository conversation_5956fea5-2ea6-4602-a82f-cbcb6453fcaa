<template>
  <div class="dashboard">
    <div class="container">
      <h2>Dashboard</h2>
      
      <!-- System Status Cards -->
      <div class="status-grid">
        <div class="card">
          <h3>System Health</h3>
          <div class="status-indicator" :class="systemHealth.status">
            <span class="badge" :class="getBadgeClass(systemHealth.status)">
              {{ systemHealth.status.toUpperCase() }}
            </span>
          </div>
          <p>Database: {{ systemHealth.database }}</p>
          <p>Uptime: {{ formatUptime(systemHealth.uptime) }}</p>
        </div>

        <div class="card">
          <h3>WooCommerce Connection</h3>
          <div class="status-indicator" :class="wooCommerceStatus">
            <span class="badge" :class="getBadgeClass(wooCommerceStatus)">
              {{ wooCommerceStatus.toUpperCase() }}
            </span>
          </div>
          <button @click="testWooCommerceConnection" class="btn btn-secondary">
            Test Connection
          </button>
        </div>

        <div class="card">
          <h3>Last Sync</h3>
          <div v-if="lastSync">
            <p><strong>Type:</strong> {{ lastSync.type }}</p>
            <p><strong>Direction:</strong> {{ lastSync.direction }}</p>
            <p><strong>Status:</strong> 
              <span class="badge" :class="getBadgeClass(lastSync.status)">
                {{ lastSync.status }}
              </span>
            </p>
            <p><strong>Time:</strong> {{ formatDate(lastSync.started_at) }}</p>
          </div>
          <div v-else>
            <p>No sync history available</p>
          </div>
        </div>

        <div class="card">
          <h3>Quick Actions</h3>
          <div class="action-buttons">
            <button @click="triggerProductSync" class="btn btn-success" :disabled="isLoading">
              Sync Products
            </button>
            <button @click="triggerOrderSync" class="btn btn-warning" :disabled="isLoading">
              Sync Orders
            </button>
            <router-link to="/sync" class="btn btn-secondary">
              View Sync History
            </router-link>
          </div>
        </div>
      </div>

      <!-- Recent Activity -->
      <div class="card">
        <h3>Recent Activity</h3>
        <div v-if="recentActivity.length > 0">
          <table class="table">
            <thead>
              <tr>
                <th>Time</th>
                <th>Type</th>
                <th>Direction</th>
                <th>Status</th>
                <th>Records</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="activity in recentActivity" :key="activity.id">
                <td>{{ formatDate(activity.started_at) }}</td>
                <td>{{ activity.type }}</td>
                <td>{{ activity.direction }}</td>
                <td>
                  <span class="badge" :class="getBadgeClass(activity.status)">
                    {{ activity.status }}
                  </span>
                </td>
                <td>{{ activity.records_processed }}/{{ activity.records_total }}</td>
              </tr>
            </tbody>
          </table>
        </div>
        <div v-else>
          <p>No recent activity</p>
        </div>
      </div>

      <!-- Loading indicator -->
      <div v-if="isLoading" class="spinner"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useApiStore } from '../stores/api'

const apiStore = useApiStore()

const isLoading = ref(false)
const systemHealth = ref({
  status: 'unknown',
  database: 'unknown',
  uptime: 0
})
const wooCommerceStatus = ref('unknown')
const lastSync = ref(null)
const recentActivity = ref([])

onMounted(async () => {
  await loadDashboardData()
})

const loadDashboardData = async () => {
  isLoading.value = true
  try {
    // Load system health
    const health = await apiStore.getHealth()
    systemHealth.value = health
    
    // Load sync status
    const syncStatus = await apiStore.getSyncStatus()
    lastSync.value = syncStatus.lastSync
    
    // Load recent activity
    const history = await apiStore.getSyncHistory(5)
    recentActivity.value = history
    
  } catch (error) {
    console.error('Failed to load dashboard data:', error)
  } finally {
    isLoading.value = false
  }
}

const testWooCommerceConnection = async () => {
  isLoading.value = true
  try {
    const result = await apiStore.testWooCommerceConnection()
    wooCommerceStatus.value = result ? 'connected' : 'disconnected'
  } catch (error) {
    wooCommerceStatus.value = 'error'
    console.error('WooCommerce connection test failed:', error)
  } finally {
    isLoading.value = false
  }
}

const triggerProductSync = async () => {
  isLoading.value = true
  try {
    await apiStore.triggerSync('products', 'inbound')
    await loadDashboardData()
  } catch (error) {
    console.error('Failed to trigger product sync:', error)
  } finally {
    isLoading.value = false
  }
}

const triggerOrderSync = async () => {
  isLoading.value = true
  try {
    await apiStore.triggerSync('orders', 'outbound')
    await loadDashboardData()
  } catch (error) {
    console.error('Failed to trigger order sync:', error)
  } finally {
    isLoading.value = false
  }
}

const getBadgeClass = (status: string) => {
  switch (status) {
    case 'ok':
    case 'connected':
    case 'completed':
      return 'badge-success'
    case 'error':
    case 'disconnected':
    case 'failed':
      return 'badge-danger'
    case 'running':
    case 'pending':
      return 'badge-warning'
    default:
      return 'badge-secondary'
  }
}

const formatUptime = (seconds: number) => {
  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  return `${hours}h ${minutes}m`
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleString()
}
</script>

<style scoped>
.status-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
}

.status-indicator {
  margin: 1rem 0;
}

.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.action-buttons .btn {
  width: 100%;
}

@media (min-width: 768px) {
  .action-buttons {
    flex-direction: row;
  }
  
  .action-buttons .btn {
    width: auto;
    flex: 1;
  }
}
</style>
