cmd_Release/obj.target/test_extension/deps/test_extension.o := cc -o Release/obj.target/test_extension/deps/test_extension.o ../deps/test_extension.c '-DNODE_GYP_MODULE_NAME=test_extension' '-DUSING_UV_SHARED=1' '-DUSING_V8_SHARED=1' '-DV8_DEPRECATION_WARNINGS=1' '-D_GLIBCXX_USE_CXX11_ABI=1' '-D_LARGEFILE_SOURCE' '-D_FILE_OFFSET_BITS=64' '-D__STDC_FORMAT_MACROS' '-DOPENSSL_NO_PINSHARED' '-DOPENSSL_THREADS' '-DBUILDING_NODE_EXTENSION' '-DNDEBUG' -I/home/<USER>/.cache/node-gyp/22.14.0/include/node -I/home/<USER>/.cache/node-gyp/22.14.0/src -I/home/<USER>/.cache/node-gyp/22.14.0/deps/openssl/config -I/home/<USER>/.cache/node-gyp/22.14.0/deps/openssl/openssl/include -I/home/<USER>/.cache/node-gyp/22.14.0/deps/uv/include -I/home/<USER>/.cache/node-gyp/22.14.0/deps/zlib -I/home/<USER>/.cache/node-gyp/22.14.0/deps/v8/include -I./Release/obj/gen/sqlite3  -fPIC -pthread -Wall -Wextra -Wno-unused-parameter -m64 -O3 -O3 -fno-omit-frame-pointer  -MMD -MF ./Release/.deps/Release/obj.target/test_extension/deps/test_extension.o.d.raw   -c
Release/obj.target/test_extension/deps/test_extension.o: \
 ../deps/test_extension.c Release/obj/gen/sqlite3/sqlite3ext.h \
 Release/obj/gen/sqlite3/sqlite3.h
../deps/test_extension.c:
Release/obj/gen/sqlite3/sqlite3ext.h:
Release/obj/gen/sqlite3/sqlite3.h:
