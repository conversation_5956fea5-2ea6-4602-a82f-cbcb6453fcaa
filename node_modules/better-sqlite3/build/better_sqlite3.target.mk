# This file is generated by gyp; do not edit.

TOOLSET := target
TARGET := better_sqlite3
DEFS_Debug := \
	'-DNODE_GYP_MODULE_NAME=better_sqlite3' \
	'-DUSING_UV_SHARED=1' \
	'-DUSING_V8_SHARED=1' \
	'-DV8_DEPRECATION_WARNINGS=1' \
	'-D_GLIBCXX_USE_CXX11_ABI=1' \
	'-D_LARGEFILE_SOURCE' \
	'-D_FILE_OFFSET_BITS=64' \
	'-D__STDC_FORMAT_MACROS' \
	'-DOPENSSL_NO_PINSHARED' \
	'-DOPENSSL_THREADS' \
	'-DBUILDING_NODE_EXTENSION' \
	'-DDEBUG' \
	'-D_DEBUG' \
	'-DSQLITE_DEBUG' \
	'-DSQLITE_MEMDEBUG' \
	'-DSQLITE_ENABLE_API_ARMOR' \
	'-DSQLITE_WIN32_MALLOC_VALIDATE'

# Flags passed to all source files.
CFLAGS_Debug := \
	-fPIC \
	-pthread \
	-Wall \
	-Wextra \
	-Wno-unused-parameter \
	-m64 \
	-g \
	-O0 \
	-O0

# Flags passed to only C files.
CFLAGS_C_Debug :=

# Flags passed to only C++ files.
CFLAGS_CC_Debug := \
	-fno-rtti \
	-fno-exceptions \
	-fno-strict-aliasing \
	-std=gnu++17 \
	-std=c++17

INCS_Debug := \
	-I/home/<USER>/.cache/node-gyp/22.14.0/include/node \
	-I/home/<USER>/.cache/node-gyp/22.14.0/src \
	-I/home/<USER>/.cache/node-gyp/22.14.0/deps/openssl/config \
	-I/home/<USER>/.cache/node-gyp/22.14.0/deps/openssl/openssl/include \
	-I/home/<USER>/.cache/node-gyp/22.14.0/deps/uv/include \
	-I/home/<USER>/.cache/node-gyp/22.14.0/deps/zlib \
	-I/home/<USER>/.cache/node-gyp/22.14.0/deps/v8/include \
	-I$(obj)/gen/sqlite3

DEFS_Release := \
	'-DNODE_GYP_MODULE_NAME=better_sqlite3' \
	'-DUSING_UV_SHARED=1' \
	'-DUSING_V8_SHARED=1' \
	'-DV8_DEPRECATION_WARNINGS=1' \
	'-D_GLIBCXX_USE_CXX11_ABI=1' \
	'-D_LARGEFILE_SOURCE' \
	'-D_FILE_OFFSET_BITS=64' \
	'-D__STDC_FORMAT_MACROS' \
	'-DOPENSSL_NO_PINSHARED' \
	'-DOPENSSL_THREADS' \
	'-DBUILDING_NODE_EXTENSION' \
	'-DNDEBUG'

# Flags passed to all source files.
CFLAGS_Release := \
	-fPIC \
	-pthread \
	-Wall \
	-Wextra \
	-Wno-unused-parameter \
	-m64 \
	-O3 \
	-O3 \
	-fno-omit-frame-pointer

# Flags passed to only C files.
CFLAGS_C_Release :=

# Flags passed to only C++ files.
CFLAGS_CC_Release := \
	-fno-rtti \
	-fno-exceptions \
	-fno-strict-aliasing \
	-std=gnu++17 \
	-std=c++17

INCS_Release := \
	-I/home/<USER>/.cache/node-gyp/22.14.0/include/node \
	-I/home/<USER>/.cache/node-gyp/22.14.0/src \
	-I/home/<USER>/.cache/node-gyp/22.14.0/deps/openssl/config \
	-I/home/<USER>/.cache/node-gyp/22.14.0/deps/openssl/openssl/include \
	-I/home/<USER>/.cache/node-gyp/22.14.0/deps/uv/include \
	-I/home/<USER>/.cache/node-gyp/22.14.0/deps/zlib \
	-I/home/<USER>/.cache/node-gyp/22.14.0/deps/v8/include \
	-I$(obj)/gen/sqlite3

OBJS := \
	$(obj).target/$(TARGET)/src/better_sqlite3.o

# Add to the list of files we specially track dependencies for.
all_deps += $(OBJS)

# Make sure our dependencies are built before any of us.
$(OBJS): | $(builddir)/sqlite3.a $(obj).target/deps/locate_sqlite3.stamp $(obj).target/deps/sqlite3.a

# CFLAGS et al overrides must be target-local.
# See "Target-specific Variable Values" in the GNU Make manual.
$(OBJS): TOOLSET := $(TOOLSET)
$(OBJS): GYP_CFLAGS := $(DEFS_$(BUILDTYPE)) $(INCS_$(BUILDTYPE))  $(CFLAGS_$(BUILDTYPE)) $(CFLAGS_C_$(BUILDTYPE))
$(OBJS): GYP_CXXFLAGS := $(DEFS_$(BUILDTYPE)) $(INCS_$(BUILDTYPE))  $(CFLAGS_$(BUILDTYPE)) $(CFLAGS_CC_$(BUILDTYPE))

# Suffix rules, putting all outputs into $(obj).

$(obj).$(TOOLSET)/$(TARGET)/%.o: $(srcdir)/%.cpp FORCE_DO_CMD
	@$(call do_cmd,cxx,1)

# Try building from generated source, too.

$(obj).$(TOOLSET)/$(TARGET)/%.o: $(obj).$(TOOLSET)/%.cpp FORCE_DO_CMD
	@$(call do_cmd,cxx,1)

$(obj).$(TOOLSET)/$(TARGET)/%.o: $(obj)/%.cpp FORCE_DO_CMD
	@$(call do_cmd,cxx,1)

# End of this set of suffix rules
### Rules for final target.
LDFLAGS_Debug := \
	-pthread \
	-rdynamic \
	-Wl,-Bsymbolic \
	-Wl,--exclude-libs,ALL \
	-m64

LDFLAGS_Release := \
	-pthread \
	-rdynamic \
	-Wl,-Bsymbolic \
	-Wl,--exclude-libs,ALL \
	-m64

LIBS :=

$(obj).target/better_sqlite3.node: GYP_LDFLAGS := $(LDFLAGS_$(BUILDTYPE))
$(obj).target/better_sqlite3.node: LIBS := $(LIBS)
$(obj).target/better_sqlite3.node: TOOLSET := $(TOOLSET)
$(obj).target/better_sqlite3.node: $(OBJS) $(obj).target/deps/sqlite3.a FORCE_DO_CMD
	$(call do_cmd,solink_module)

all_deps += $(obj).target/better_sqlite3.node
# Add target alias
.PHONY: better_sqlite3
better_sqlite3: $(builddir)/better_sqlite3.node

# Copy this to the executable output path.
$(builddir)/better_sqlite3.node: TOOLSET := $(TOOLSET)
$(builddir)/better_sqlite3.node: $(obj).target/better_sqlite3.node FORCE_DO_CMD
	$(call do_cmd,copy)

all_deps += $(builddir)/better_sqlite3.node
# Short alias for building this executable.
.PHONY: better_sqlite3.node
better_sqlite3.node: $(obj).target/better_sqlite3.node $(builddir)/better_sqlite3.node

# Add executable to "all" target.
.PHONY: all
all: $(builddir)/better_sqlite3.node

